import re
import time
import requests
def coze_api(api_key, bot_id, query, user, conversation_id):
        start = time.time()
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "Accept": "*/*",
            "Host": "api.coze.cn",
            "Connection": "keep-alive",
        }
        url = "https://api.coze.cn/open_api/v2/chat"
        data = {
            "conversation_id": conversation_id,
            "bot_id": f"{bot_id}",
            "user": user,
            "query": query,
            "stream": False,
        }
        response = requests.post(url, headers=headers, json=data).json()
        content=response["messages"][0]['content']
     
        end = time.time()
        print("耗时：", end - start)

        return content

if __name__=="__main__":
    api_key="pat_6yFobaFJKTfsGLTDIhYzArjru3zgDqkykzZM2O7t1gkA3v5gGkOyiaFnYMfvFGTj"
    bot_id="7515411875912368164"
    user="blue"
    conversation_id="3446416484146548"
    query="地址证明是什么"
    response=coze_api(api_key,bot_id,query,user,conversation_id)
    print(response)
    response1="\n".join([line for line in response.split("\\n") if line.strip()])
    print(response1)
