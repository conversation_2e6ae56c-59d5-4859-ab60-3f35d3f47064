# -*- coding: utf-8 -*-
"""
性能监控模块
监控系统资源使用情况，检测性能问题
"""

import threading
import time
import psutil
import logging
import gc
from typing import Dict, Any
from performance_config import get_config

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.config = get_config('monitoring')
        self.running = False
        self.monitor_thread = None
        self.stats = {
            'cpu_usage': 0,
            'memory_usage': 0,
            'memory_percent': 0,
            'thread_count': 0,
            'start_time': time.time(),
            'message_count': 0,
            'error_count': 0,
            'slow_operations': 0,
        }
        self.operation_times = []
        self.lock = threading.Lock()
        
    def start(self):
        """启动性能监控"""
        if self.running:
            return
            
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("性能监控已启动")
        
    def stop(self):
        """停止性能监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("性能监控已停止")
        
    def record_operation(self, operation_name: str, duration: float):
        """记录操作耗时"""
        with self.lock:
            self.operation_times.append({
                'name': operation_name,
                'duration': duration,
                'timestamp': time.time()
            })
            
            # 检查是否为慢操作
            threshold = self.config.get('slow_operation_threshold', 10)
            if duration > threshold:
                self.stats['slow_operations'] += 1
                self.logger.warning(f"慢操作检测: {operation_name} 耗时 {duration:.2f}s")
                
            # 清理旧记录
            current_time = time.time()
            self.operation_times = [
                op for op in self.operation_times 
                if current_time - op['timestamp'] < 3600  # 保留1小时内的记录
            ]
            
    def record_message(self):
        """记录消息处理"""
        with self.lock:
            self.stats['message_count'] += 1
            
    def record_error(self):
        """记录错误"""
        with self.lock:
            self.stats['error_count'] += 1
            
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            current_stats = self.stats.copy()
            current_stats['uptime'] = time.time() - self.stats['start_time']
            current_stats['avg_cpu'] = sum(op['duration'] for op in self.operation_times[-100:]) / max(len(self.operation_times[-100:]), 1)
            return current_stats
            
    def _monitor_loop(self):
        """监控主循环"""
        while self.running:
            try:
                # 获取系统资源使用情况
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_info = psutil.virtual_memory()
                process = psutil.Process()
                
                with self.lock:
                    self.stats['cpu_usage'] = cpu_percent
                    self.stats['memory_usage'] = process.memory_info().rss / 1024 / 1024  # MB
                    self.stats['memory_percent'] = memory_info.percent
                    self.stats['thread_count'] = threading.active_count()
                
                # 检查警告阈值
                self._check_thresholds(cpu_percent, memory_info.percent, self.stats['memory_usage'])
                
                # 定期清理和垃圾回收
                if int(time.time()) % 300 == 0:  # 每5分钟
                    self._cleanup_and_gc()
                    
            except Exception as e:
                self.logger.error(f"性能监控异常: {e}")
                
            time.sleep(10)  # 每10秒检查一次
            
    def _check_thresholds(self, cpu_percent: float, memory_percent: float, process_memory: float):
        """检查性能阈值"""
        cpu_threshold = self.config.get('cpu_warning_threshold', 80)
        memory_threshold = self.config.get('memory_warning_threshold', 500)
        
        if cpu_percent > cpu_threshold:
            self.logger.warning(f"CPU使用率过高: {cpu_percent:.1f}%")
            
        if process_memory > memory_threshold:
            self.logger.warning(f"进程内存使用过高: {process_memory:.1f}MB")
            
        if memory_percent > 90:
            self.logger.warning(f"系统内存使用率过高: {memory_percent:.1f}%")
            
    def _cleanup_and_gc(self):
        """清理和垃圾回收"""
        try:
            # 强制垃圾回收
            collected = gc.collect()
            if collected > 0:
                self.logger.debug(f"垃圾回收清理了 {collected} 个对象")
                
            # 清理操作记录
            current_time = time.time()
            with self.lock:
                old_count = len(self.operation_times)
                self.operation_times = [
                    op for op in self.operation_times 
                    if current_time - op['timestamp'] < 1800  # 保留30分钟内的记录
                ]
                if old_count > len(self.operation_times):
                    self.logger.debug(f"清理了 {old_count - len(self.operation_times)} 条操作记录")
                    
        except Exception as e:
            self.logger.error(f"清理和垃圾回收异常: {e}")
            
    def get_performance_report(self) -> str:
        """生成性能报告"""
        stats = self.get_stats()
        uptime_hours = stats['uptime'] / 3600
        
        report = f"""
=== 性能监控报告 ===
运行时间: {uptime_hours:.1f} 小时
CPU使用率: {stats['cpu_usage']:.1f}%
进程内存: {stats['memory_usage']:.1f}MB
系统内存: {stats['memory_percent']:.1f}%
活跃线程: {stats['thread_count']}
处理消息: {stats['message_count']}
错误次数: {stats['error_count']}
慢操作: {stats['slow_operations']}
平均响应时间: {stats['avg_cpu']:.2f}s
"""
        return report.strip()

# 全局性能监控实例
_performance_monitor = None

def get_performance_monitor(logger=None):
    """获取全局性能监控实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor(logger)
    return _performance_monitor
