# -*- coding: utf-8 -*-
"""
性能优化配置文件
包含各种超时、重试、缓存等性能相关的配置参数
"""

# 网络连接配置
NETWORK_CONFIG = {
    'connection_timeout': 30,  # 连接超时（秒）
    'read_timeout': 60,        # 读取超时（秒）
    'max_retries': 3,          # 最大重试次数
    'retry_delay': 2,          # 重试延迟（秒）
    'heartbeat_interval': 60,  # 心跳检测间隔（秒）
    'reconnect_delay': 30,     # 重连延迟（秒）
    'max_reconnect_attempts': 5,  # 最大重连尝试次数
}

# 消息处理配置
MESSAGE_CONFIG = {
    'max_workers': 3,          # 消息处理线程池大小
    'queue_timeout': 1,        # 队列超时（秒）
    'duplicate_window': 30,    # 消息去重时间窗口（秒）
    'cleanup_interval': 300,   # 清理间隔（秒）
    'max_cached_messages': 1000,  # 最大缓存消息数
    'processing_timeout': 30,  # 单条消息处理超时（秒）
}

# Coze工作流配置
COZE_CONFIG = {
    'workflow_timeout': 30,    # 工作流调用超时（秒）
    'max_retries': 3,          # 最大重试次数
    'retry_delay': 2,          # 重试延迟（秒）
    'rate_limit_delay': 1,     # 速率限制延迟（秒）
}

# 内存管理配置
MEMORY_CONFIG = {
    'gc_interval': 300,        # 垃圾回收间隔（秒）
    'max_log_entries': 10000,  # 最大日志条目数
    'cache_cleanup_interval': 600,  # 缓存清理间隔（秒）
}

# 日志配置
LOGGING_CONFIG = {
    'max_file_size': 10 * 1024 * 1024,  # 最大日志文件大小（10MB）
    'backup_count': 5,         # 日志文件备份数量
    'log_level': 'INFO',       # 日志级别
    'performance_log': True,   # 是否启用性能日志
}

# 监控配置
MONITORING_CONFIG = {
    'enable_performance_monitoring': True,  # 启用性能监控
    'slow_operation_threshold': 10,         # 慢操作阈值（秒）
    'memory_warning_threshold': 500,        # 内存警告阈值（MB）
    'cpu_warning_threshold': 80,            # CPU警告阈值（%）
}

def get_config(config_type: str) -> dict:
    """
    获取指定类型的配置
    
    Args:
        config_type: 配置类型 ('network', 'message', 'coze', 'memory', 'logging', 'monitoring')
    
    Returns:
        dict: 配置字典
    """
    config_map = {
        'network': NETWORK_CONFIG,
        'message': MESSAGE_CONFIG,
        'coze': COZE_CONFIG,
        'memory': MEMORY_CONFIG,
        'logging': LOGGING_CONFIG,
        'monitoring': MONITORING_CONFIG,
    }
    
    return config_map.get(config_type, {})

def update_config(config_type: str, updates: dict):
    """
    更新配置
    
    Args:
        config_type: 配置类型
        updates: 要更新的配置项
    """
    config_map = {
        'network': NETWORK_CONFIG,
        'message': MESSAGE_CONFIG,
        'coze': COZE_CONFIG,
        'memory': MEMORY_CONFIG,
        'logging': LOGGING_CONFIG,
        'monitoring': MONITORING_CONFIG,
    }
    
    if config_type in config_map:
        config_map[config_type].update(updates)
