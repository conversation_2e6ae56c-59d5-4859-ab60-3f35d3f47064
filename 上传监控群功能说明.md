# 上传监控群和监控好友功能说明

## 功能概述
在群管理tab和定时任务tab中新增了批量上传功能，支持通过txt文件批量添加监控群和监控好友，实现快速筛选和批量管理功能。

## 新增功能

### 群管理tab功能

#### 1. 上传监控群按钮
- 位置：群管理tab -> 监控群列表区域 -> 顶部控制栏
- 功能：点击后可选择txt文件，批量添加监控群

#### 2. 清空列表按钮
- 位置：群管理tab -> 监控群列表区域 -> 顶部控制栏
- 功能：一键清空所有监控群列表

### 定时任务tab功能

#### 3. 上传监控群按钮
- 位置：定时任务tab -> 发送目标右侧
- 功能：在群聊模式下，批量选择上传的群作为发送目标

#### 4. 上传监控好友按钮
- 位置：定时任务tab -> 发送目标右侧
- 功能：在私聊模式下，批量选择上传的好友作为发送目标

## 使用方法

### 准备txt文件

#### 群名称文件
1. 创建一个txt文件（如：监控群列表.txt）
2. 每行写一个群名称，例如：
   ```
   测试群1
   测试群2
   客服群
   技术支持群
   产品讨论群
   ```

#### 好友名称文件
1. 创建一个txt文件（如：监控好友列表.txt）
2. 每行写一个好友名称（真实姓名或用户名），例如：
   ```
   邾俊勇
   林铭清
   陆冰清
   刘羿
   邵康杰
   ```

### 群管理tab操作
1. 启动程序，等待群列表加载完成
2. 切换到"群管理"选项卡
3. 在右侧"监控群列表"区域，点击"上传监控群"按钮
4. 在文件对话框中选择准备好的txt文件
5. 程序会自动：
   - 读取文件中的群名称
   - 在所有群列表中查找匹配的群
   - 将找到的群添加到监控列表
   - 显示添加结果和未找到的群名称

### 定时任务tab操作

#### 上传监控群
1. 启动程序，等待群列表和好友列表加载完成
2. 切换到"定时任务"选项卡
3. 在发送目标选择"群聊"
4. 点击右侧"上传监控群"按钮
5. 选择群名称txt文件
6. 程序会自动在目标列表中选择匹配的群

#### 上传监控好友
1. 启动程序，等待群列表和好友列表加载完成
2. 切换到"定时任务"选项卡
3. 在发送目标选择"私聊"
4. 点击右侧"上传监控好友"按钮
5. 选择好友名称txt文件
6. 程序会自动在目标列表中选择匹配的好友

### 注意事项
1. **名称匹配**：群名称和好友名称必须完全匹配（区分大小写）
2. **去重处理**：
   - 群管理tab：已存在于监控列表中的群不会重复添加
   - 定时任务tab：会清除之前的选择，重新选择匹配的目标
3. **模式检查**：定时任务tab中的按钮会检查当前选择的发送目标类型
4. **结果反馈**：如果有名称未找到，会在结果对话框中显示
5. **文件格式**：支持.txt文件，编码为UTF-8

## 示例文件
项目中包含了两个示例文件：
- `监控群列表示例.txt`：群名称示例文件
- `监控好友列表示例.txt`：好友名称示例文件

## 技术实现
- 使用tkinter.filedialog进行文件选择
- 支持UTF-8编码的txt文件读取
- 实现了名称精确匹配算法
- 支持真实姓名和用户名双重匹配（好友）
- 提供详细的操作结果反馈
- 智能去重和选择管理
