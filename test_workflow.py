#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Coze工作流集成
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_coze_workflow():
    """测试Coze工作流功能"""
    try:
        from coze.coze_api_key import CozeWorkflow
        
        # 创建工作流实例
        coze_workflow = CozeWorkflow()
        
        # 测试工作流ID（请替换为实际的工作流ID）
        workflow_id = "7509123255656087564"
        query = "你好，这是一个测试消息"
        
        print(f"正在测试工作流ID: {workflow_id}")
        print(f"测试查询: {query}")
        
        # 调用工作流
        response = coze_workflow.run_workflow(workflow_id, query)
        
        print(f"工作流响应: {response}")
        print("测试成功！")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试Coze工作流集成...")
    success = test_coze_workflow()
    
    if success:
        print("\n✅ 所有测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
