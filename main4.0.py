# -*- coding: utf-8 -*-
import sys, time, re, os
import configparser
import logging, ntwork
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from datetime import datetime
import json
import keyboard
import subprocess
import requests
import ttkbootstrap as ttk
from ttkbootstrap import Style
from ttkbootstrap.constants import *  # 导入常量
import order_query
# 在文件顶部添加一个全局变量来跟踪实例
_instance = None

# 自定义日志处理器，将日志输出到GUI
class GuiHandler(logging.Handler):
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)

        def append():
            self.text_widget.configure(state="normal")
            self.text_widget.insert(tk.END, msg + "\n")
            self.text_widget.see(tk.END)
            self.text_widget.configure(state="disabled")

        self.text_widget.after(0, append)


class WeChatGUI:
    def __init__(self):
        global _instance
        if _instance is not None:
            raise RuntimeError("WeChatGUI 已经在运行")
        _instance = self
        
        # 使用 ttkbootstrap 的样式
        self.style = Style(theme='cyborg')  # 使用深色科技风主题
        self.root = self.style.master
        self.root.title("企业微信订单查询监控")
        self.root.geometry("1000x750")  # 从800改为750
        
        # 设置窗口在屏幕中央
        self.center_window()
        
        # 设置窗口图标（如果有的话）
        # self.root.iconbitmap('path/to/icon.ico')
        
        self.startup = None
        
        # 创建主框架
        self.create_widgets()
        self.setup_logging()
        self.load_config()
        self.init_wechat()

    def __del__(self):
        global _instance
        _instance = None

    def center_window(self):
        """将窗口居中显示"""
        # 获取屏幕的宽度和高度
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 获取窗口的宽度和高度
        window_width = 1000
        window_height = 800  # 从800改为750
        
        # 计算窗口居中的坐标
        center_x = int((screen_width - window_width) / 2)
        center_y = int((screen_height - window_height) / 2)
        
        # 设置窗口位置
        self.root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")

    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
        )
        self.logger = logging.getLogger()

        # 添加GUI处理器
        gui_handler = GuiHandler(self.log_text)
        gui_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        )
        self.logger.addHandler(gui_handler)

    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 群列表管理区域
        room_frame = ttk.LabelFrame(main_frame, text="群管理", padding="5", bootstyle="primary")
        room_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=(5,2))

        # 左侧群列表
        left_frame = ttk.Frame(room_frame)
        left_frame.grid(row=0, column=0, padx=5, sticky=tk.NSEW)
        
        # 添加标题和刷新按钮的容器
        title_frame = ttk.Frame(left_frame)
        title_frame.grid(row=0, column=0, sticky=tk.EW, pady=(0,5))
        
        ttk.Label(title_frame, text="所有群列表", bootstyle="inverse-primary").pack(side=tk.LEFT)
        # ttk.Button(title_frame, text="刷新", command=self.refresh_room_list, bootstyle="info-outline").pack(side=tk.RIGHT)
        
        # 创建滚动条和树形视图的框架
        tree_frame = ttk.Frame(left_frame)
        tree_frame.grid(row=1, column=0, sticky=tk.NSEW)
        
        self.all_rooms_tree = ttk.Treeview(
            tree_frame, 
            columns=('room_id',),
            height=8,
            bootstyle="primary"
        )
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.all_rooms_tree.yview, bootstyle="primary-round")
        self.all_rooms_tree.configure(yscrollcommand=scrollbar.set)
        
        self.all_rooms_tree['show'] = 'tree headings'
        self.all_rooms_tree.heading('#0', text='群名称')
        self.all_rooms_tree.heading('room_id', text='群ID')
        self.all_rooms_tree.column('#0', width=200, stretch=True)
        self.all_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.all_rooms_tree.grid(row=0, column=0, sticky=tk.NSEW)
        scrollbar.grid(row=0, column=1, sticky=tk.NS)

        # 中间操作按钮
        btn_frame = ttk.Frame(room_frame)
        btn_frame.grid(row=0, column=1, padx=10)
        
        # 刷新按钮移到这里
        ttk.Button(btn_frame, text="刷新列表", command=self.refresh_room_list, bootstyle="info-outline").pack(pady=5)
        ttk.Button(btn_frame, text="添加 >>", command=self.add_selected_room, bootstyle="primary-outline").pack(pady=5)
        ttk.Button(btn_frame, text="<< 移除", command=self.remove_selected_room, bootstyle="danger-outline").pack(pady=5)

        # 右侧监控群列表
        right_frame = ttk.Frame(room_frame)
        right_frame.grid(row=0, column=2, padx=5, sticky=tk.NSEW)
        
        ttk.Label(right_frame, text="监控群列表", bootstyle="inverse-primary").grid(row=0, column=0, sticky=tk.W, pady=(0,5))
        
        # 监控群树形视图
        monitor_frame = ttk.Frame(right_frame)
        monitor_frame.grid(row=1, column=0, sticky=tk.NSEW)
        
        self.monitored_rooms_tree = ttk.Treeview(
            monitor_frame,
            columns=('room_id',),
            height=8,
            bootstyle="primary"
        )
        monitor_scrollbar = ttk.Scrollbar(monitor_frame, orient="vertical", command=self.monitored_rooms_tree.yview, bootstyle="primary-round")
        self.monitored_rooms_tree.configure(yscrollcommand=monitor_scrollbar.set)
        
        self.monitored_rooms_tree['show'] = 'tree headings'
        self.monitored_rooms_tree.heading('#0', text='群名称')
        self.monitored_rooms_tree.heading('room_id', text='群ID')
        self.monitored_rooms_tree.column('#0', width=200, stretch=True)
        self.monitored_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.monitored_rooms_tree.grid(row=0, column=0, sticky=tk.NSEW)
        monitor_scrollbar.grid(row=0, column=1, sticky=tk.NS)

        # 创建水平分割的框架
        config_container = ttk.Frame(main_frame)
        config_container.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        config_container.columnconfigure(0, weight=1)
        config_container.columnconfigure(1, weight=1)

        # API参数配置区域 - 左侧
        config_frame = ttk.LabelFrame(config_container, text="API参数配置", padding="5", bootstyle="info")
        config_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 2.5))
        
        ttk.Label(config_frame, text="主订单提示:", bootstyle="inverse-info").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.main_order_msg = ttk.Text(config_frame, width=50, height=2, wrap=tk.WORD)
        self.main_order_msg.grid(row=2, column=1, padx=5, pady=5)

        ttk.Label(config_frame, text="回复前缀:", bootstyle="inverse-info").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.reply_prefix = ttk.Text(config_frame, width=50, height=2, wrap=tk.WORD)
        self.reply_prefix.grid(row=0, column=1, padx=5, pady=5)
        self.reply_prefix.insert("1.0", "你好！@{sender_name}\n订单号：{order_id}")  # 默认值
        
        ttk.Label(config_frame, text="无数据提示:", bootstyle="inverse-info").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.no_data_msg = ttk.Text(config_frame, width=50, height=2, wrap=tk.WORD)
        self.no_data_msg.grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(config_frame, text="无效订单提示:", bootstyle="inverse-info").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.invalid_order_msg = ttk.Text(config_frame, width=50, height=2, wrap=tk.WORD)
        self.invalid_order_msg.grid(row=3, column=1, padx=5, pady=5)

        ttk.Label(config_frame, text="待付款提示:", bootstyle="inverse-info").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.pending_payment_msg = ttk.Text(config_frame, width=50, height=2, wrap=tk.WORD)
        self.pending_payment_msg.grid(row=4, column=1, padx=5, pady=5)

        ttk.Label(config_frame, text="已付款提示:", bootstyle="inverse-info").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.paid_order_msg = ttk.Text(config_frame, width=50, height=2, wrap=tk.WORD)
        self.paid_order_msg.grid(row=5, column=1, padx=5, pady=5)

        ttk.Label(config_frame, text="已完成提示:", bootstyle="inverse-info").grid(row=7, column=0, sticky=tk.W, pady=5)
        self.finished_order_msg = ttk.Text(config_frame, width=50, height=2, wrap=tk.WORD)
        self.finished_order_msg.grid(row=7, column=1, padx=5, pady=5)

        ttk.Label(config_frame, text="已付定金提示:", bootstyle="inverse-info").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.paid_deposit_order_msg = ttk.Text(config_frame, width=50, height=2, wrap=tk.WORD)
        self.paid_deposit_order_msg.grid(row=6, column=1, padx=5, pady=5)

        # 日志显示区 - 右侧
        log_frame = ttk.LabelFrame(config_container, text="运行日志", padding="5", bootstyle="secondary")
        log_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(2.5, 5))

        self.log_text = ttk.Text(log_frame, width=50, height=20)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.log_text.configure(state="disabled")

        # 控制按钮和保存配置按钮放在同一行
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=3)
        
        # 左侧放监控控制按钮
        control_left = ttk.Frame(control_frame)
        control_left.pack(side=tk.LEFT)
        
        self.start_btn = ttk.Button(control_left, text="启动监控", command=self.start_monitoring, bootstyle="success")
        self.start_btn.pack(side=tk.LEFT, padx=2)
        
        self.stop_btn = ttk.Button(control_left, text="停止监控", command=self.stop_monitoring, state="disabled", bootstyle="danger")
        self.stop_btn.pack(side=tk.LEFT, padx=2)
        
        # 右侧放保存配置按钮，靠近停止按钮
        save_btn = ttk.Button(control_left, text="保存配置", command=self.save_config, bootstyle="success-outline")
        save_btn.pack(side=tk.LEFT, padx=2)
        
        # 调整各区域的间距
        room_frame.grid(padx=3, pady=(3,2))
        config_container.grid(pady=(2,3))
        
        # 配置grid权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        room_frame.columnconfigure((0, 2), weight=1)
        left_frame.columnconfigure(0, weight=1)
        right_frame.columnconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)

        # 配置文本框高度
        for text_widget in [self.reply_prefix, self.no_data_msg, self.invalid_order_msg, 
                           self.main_order_msg, self.pending_payment_msg, self.paid_order_msg,
                           self.finished_order_msg, self.paid_deposit_order_msg]:
            text_widget.configure(height=2)  # 统一设置文本框高度为2

    def init_wechat(self):
        def run():
            try:
                # 确保之前的实例被清理
                if self.startup:
                    try:
                        # 注册空回调替代注销
                        self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
                    except:
                        pass
                    self.startup = None
                    time.sleep(1)  # 等待资源释放
                
                # 初始化新实例
                self.startup = StartUp(self)
                
                try:
                    self.startup.wechat.open(smart=True)
                except Exception as e:
                    self.logger.error(f"打开企业微信失败: {e}")
                    # 尝试重新初始化 ntwork
                    ntwork.exit_()
                    time.sleep(2)
                    self.startup.wechat = ntwork.WeWork()
                    self.startup.wechat.open(smart=True)
                
                self.logger.info("等待登录......")
                self.startup.wechat.wait_login(timeout=60)
                self.logger.info("登录成功，等待数据同步...")
                time.sleep(5)  # 等待数据同步
                
                # 添加重试机制获取登录信息
                max_retries = 3
                for i in range(max_retries):
                    try:
                        login_info = self.startup.wechat.get_login_info()
                        if login_info and 'user_id' in login_info and 'nickname' in login_info:
                            self.startup.user_id = login_info["user_id"]
                            self.startup.name = login_info["nickname"]
                            self.logger.info(f"登录信息: user_id:{self.startup.user_id}, name:{self.startup.name}")
                            break
                        else:
                            if i < max_retries - 1:
                                self.logger.warning(f"获取登录信息不完整，正在重试... ({i + 1}/{max_retries})")
                                time.sleep(2)  # 等待2秒后重试
                            else:
                                raise RuntimeError("无法获取完整的登录信息")
                    except Exception as e:
                        if i < max_retries - 1:
                            self.logger.warning(f"获取登录信息失败，正在重试... ({i + 1}/{max_retries})")
                            time.sleep(2)
                        else:
                            raise

                # 获取群列表并加载到界面
                self.logger.info("正在获取群列表...")
                rooms = self.startup.wechat.get_rooms()
                self.logger.info(f"获取到的群列表数据: {rooms}")

                if not rooms:
                    self.logger.error("获取群列表失败，返回数据为空")
                    return

                # 清空现有列表
                for item in self.all_rooms_tree.get_children():
                    self.all_rooms_tree.delete(item)
                    
                # 添加新的群列表
                room_list = rooms.get('room_list', [])
                if not room_list:
                    self.logger.warning("群列表为空")
                    return

                for room in room_list:
                    try:
                        nickname = room.get('nickname', '未知群名')
                        conversation_id = room.get('conversation_id', '')
                        if not conversation_id:
                            continue
                            
                        self.all_rooms_tree.insert('', 'end', 
                            text=nickname,
                            values=(conversation_id,),
                            tags=('room',))
                        self.logger.info(f"加载群: {nickname} ({conversation_id})")
                    except Exception as e:
                        self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                        continue
                
                self.logger.info(f"群列表加载完成，共 {len(room_list)} 个群")
                
                
                
            except Exception as e:
                self.logger.error(f"初始化微信失败: {str(e)}")
                self.logger.error(f"错误详情: ", exc_info=True)
                if self.startup:
                    self.startup.exit_program()
                    self.startup = None
                # 重置按钮状态
                self.root.after(0, self._reset_button_state)

        # 在新线程中初始化微信
        threading.Thread(target=run, daemon=True).start()

    def start_monitoring(self):
        global _instance
        if _instance is not self:
            self.logger.error("检测到多个程序实例，请关闭其他窗口")
            return
            
        # 检查是否已登录
        if not self.startup:
            self.init_wechat()
            # self.logger.error("企业微信未初始化，请重启程序")
            return
            
        try:
            login_info = self.startup.wechat.get_login_info()
            if not login_info:
                self.logger.error("企业微信未登录，请先登录")
                return
        except:
            self.logger.error("获取登录状态失败")
            return
            
        self.save_config()  # 启动前保存当前配置
        
        def init_and_start():
            # 启动 order_query.py 的 API 服务
            try:
                # 获取当前执行文件的路径
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的执行文件
                    application_path = os.path.dirname(sys._MEIPASS)
                else:
                    # 如果是源码运行
                    application_path = os.path.dirname(os.path.abspath(__file__))
                    
                # 设置工作目录
                os.chdir(application_path)
                print('sys.executable'.center(20,'-'),sys.executable)#python路径
                print("application_path".center(20,'-'),application_path)
                # 直接运行 order_query.py
                if getattr(sys, 'frozen', False):
                    # 在打包环境中使用不同的启动方式
                    self.api_process = subprocess.Popen(
                        [sys.executable, os.path.join(application_path, "order_query.py")],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        cwd=application_path,  # 指定工作目录
                        env={"PYTHONPATH": application_path}  # 设置 Python 路径
                    )
                else:
                    # 源码环境下的启动方式
                    self.api_process = subprocess.Popen(
                        [sys.executable, "order_query.py"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        creationflags=subprocess.CREATE_NO_WINDOW
                    )
                    
                self.logger.info("API 服务启动成功")
                
                # 等待API服务启动
                time.sleep(2)
                
                # 测试API是否正常运行
                try:
                    response = requests.get("http://localhost:8002/health")
                    if response.status_code == 200:
                        self.logger.info("API 服务运行正常")
                        # 在主线程中更新UI和注册消息回调
                        self.root.after(0, lambda: self._complete_startup())
                    else:
                        self.logger.warning("API 服务响应异常")
                        self.root.after(0, self._reset_button_state)
                except requests.exceptions.ConnectionError:
                    self.logger.warning("无法连接到 API 服务，请检查服务是否正常运行")
                    self.root.after(0, self._reset_button_state)
                    
            except Exception as e:
                self.logger.error(f"启动 API 服务失败: {e}")
                self.root.after(0, self._reset_button_state)

        # 禁用启动按钮，避免重复点击
        self.start_btn.configure(state="disabled")
        # 在新线程中执行初始化
        threading.Thread(target=init_and_start, daemon=True).start()

    def _complete_startup(self):
        """在主线程中完成启动过程"""
        self.stop_btn.configure(state="normal")
        # 注册消息回调
        self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(self.startup.on_recv_message)
        self.logger.info("监控已启动")

    def stop_monitoring(self):
        if hasattr(self, 'api_process'):
            try:
                self.api_process.terminate()
                self.api_process.wait(timeout=5)
                self.logger.info("API 服务已停止")
            except Exception as e:
                self.logger.error(f"停止 API 服务时出错: {e}")
            finally:
                self.api_process = None
            
        if self.startup:
            try:
                # 注销消息回调的替代方案
                self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)  # 注册一个空回调
                self.startup.exit_flag = True
                self.logger.info("消息回调已注销")
            except Exception as e:
                self.logger.error(f"注销消息回调时出错: {e}")
            finally:
                self.startup = None

        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.logger.info("监控已停止")
        
        # 等待一小段时间确保资源释放
        time.sleep(1)

    def run(self):
        try:
            self.root.mainloop()
        finally:
            global _instance
            _instance = None
            # 完全退出时才清理 ntwork
            try:
                ntwork.exit_()
            except:
                pass

    def load_config(self):
        try:
            with open("config.ini", "r", encoding="utf-8") as f:
                config = json.load(f)
                # 清空并设置新的文本
                self.reply_prefix.delete("1.0", tk.END)
                self.reply_prefix.insert("1.0", config.get('reply_prefix', '你好！@{sender_name}\n订单号：{order_id}'))
                self.no_data_msg.delete("1.0", tk.END)
                self.no_data_msg.insert("1.0", config.get('no_data_msg', '该订单号未查到有效信息 😞\n tips:请等待15秒再查，15秒后若任无有效信息，请重新下单 😔'))
                self.invalid_order_msg.delete("1.0", tk.END)
                self.invalid_order_msg.insert("1.0", config.get('invalid_order_msg', '无效订单 😕\n请重新下单 😟'))
                self.main_order_msg.delete("1.0", tk.END)
                self.main_order_msg.insert("1.0", config.get('main_order_msg', '主订单'))
                self.pending_payment_msg.delete("1.0", tk.END)
                self.pending_payment_msg.insert("1.0", config.get('pending_payment_msg', '订单有效 😊\n可直接付款，无需复核 👍'))
                self.paid_order_msg.delete("1.0", tk.END)
                self.paid_order_msg.insert("1.0", config.get('paid_order_msg', '订单已付款 🎉\n跟团成功 🎊：\n群内接龙：接龙锁定跟团福利，系统已确认跟团有效 🥳'))
                self.finished_order_msg.delete("1.0", tk.END)
                self.finished_order_msg.insert("1.0", config.get('finished_order_msg', '订单已完成 🎉\n跟团成功 🎊'))
                self.paid_deposit_order_msg.delete("1.0", tk.END)
                self.paid_deposit_order_msg.insert("1.0", config.get('paid_deposit_order_msg', '已付定金订单'))
                
                # 加载监控群列表
                monitored_rooms = config.get('monitored_rooms', [])
                for room in monitored_rooms:
                    new_id = f"monitored_{room['id']}"
                    self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                        text=room['name'],
                        values=(room['id'],))
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")

    def save_config(self):
        config = {
            'reply_prefix': self.reply_prefix.get("1.0", "end-1c"),
            'no_data_msg': self.no_data_msg.get("1.0", "end-1c"),
            'invalid_order_msg': self.invalid_order_msg.get("1.0", "end-1c"),
            'main_order_msg': self.main_order_msg.get("1.0", "end-1c"),
            'pending_payment_msg': self.pending_payment_msg.get("1.0", "end-1c"),
            'paid_order_msg': self.paid_order_msg.get("1.0", "end-1c"),
            'finished_order_msg': self.finished_order_msg.get("1.0", "end-1c"),
            'paid_deposit_order_msg': self.paid_deposit_order_msg.get("1.0", "end-1c"),
            'monitored_rooms': [
                {
                    'name': self.monitored_rooms_tree.item(item)['text'],
                    'id': self.monitored_rooms_tree.item(item)['values'][0]
                }
                for item in self.monitored_rooms_tree.get_children()
            ]
        }
        try:
            with open('config.ini', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            self.logger.info("配置保存成功")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")

    def add_selected_room(self):
        selected = self.all_rooms_tree.selection()
        for item in selected:
            name = self.all_rooms_tree.item(item, 'text')
            room_id = self.all_rooms_tree.item(item, 'values')[0]
            # 检查是否已经存在
            exists = False
            for existing in self.monitored_rooms_tree.get_children():
                if self.monitored_rooms_tree.item(existing)['values'][0] == room_id:
                    exists = True
                    break
            
            if not exists:
                # 使用新的ID以避免冲突
                new_id = f"monitored_{room_id}"
                self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                    text=name,  # 显示群名称
                    values=(room_id,))  # 保存群ID
                self.logger.info(f"添加监控群：{name}")

    def remove_selected_room(self):
        selected = self.monitored_rooms_tree.selection()
        for item in selected:
            self.monitored_rooms_tree.delete(item)

    def _reset_button_state(self):
        """重置按钮状态"""
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")

    def refresh_room_list(self):
        if not self.startup or not self.startup.wechat:
            self.logger.error("企业微信未初始化，无法刷新群列表")
            return
        
        try:
            # 获取群列表
            rooms = self.startup.wechat.get_rooms()
            if not rooms:
                self.logger.error("获取群列表失败，返回数据为空")
                return
            
            # 清空现有列表
            for item in self.all_rooms_tree.get_children():
                self.all_rooms_tree.delete(item)
            
            # 添加新的群列表
            room_list = rooms.get('room_list', [])
            if not room_list:
                self.logger.warning("群列表为空")
                return
            
            for room in room_list:
                try:
                    nickname = room.get('nickname', '未知群名')
                    conversation_id = room.get('conversation_id', '')
                    if not conversation_id:
                        continue
                    
                    self.all_rooms_tree.insert('', 'end', 
                        text=nickname,
                        values=(conversation_id,),
                        tags=('room',))
                except Exception as e:
                    self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                    continue
                
            self.logger.info(f"群列表刷新完成，共 {len(room_list)} 个群")
        except Exception as e:
            self.logger.error(f"刷新群列表失败: {e}")


# 修改StartUp类
class StartUp:
    def __init__(self, gui_instance):
        self.logger = logging.getLogger()
        self.wechat = ntwork.WeWork()
        self.exit_flag = False
        self.user_id = None
        self.name = None
        self.gui = gui_instance

    def on_recv_message(self, wechat_instance: ntwork.WeWork, message):
        data = message["data"]
        content = data.get("content", "").strip()
        room_wxid = data.get("conversation_id")
        from_wxid = data.get("sender")
        sender_name = data.get("sender_name")

        # 检查是否是监控的群
        monitored_rooms = [
            self.gui.monitored_rooms_tree.item(item)['values'][0]
            for item in self.gui.monitored_rooms_tree.get_children()
        ]
        
        if from_wxid != self.user_id and room_wxid in monitored_rooms:
            numbers = re.findall(r'\d{12,}', content)
        
            if len(numbers)==1:
                order_id = numbers[0]
                params = {
                    'order_id': order_id,
                    'no_data_msg': self.gui.no_data_msg.get("1.0", "end-1c"),
                    'invalid_order_msg': self.gui.invalid_order_msg.get("1.0", "end-1c"),
                    'main_order_msg': self.gui.main_order_msg.get("1.0", "end-1c"),
                    'pending_payment_msg': self.gui.pending_payment_msg.get("1.0", "end-1c"),
                    'paid_order_msg': self.gui.paid_order_msg.get("1.0", "end-1c"),
                    'finished_order_msg': self.gui.finished_order_msg.get("1.0", "end-1c"),
                    'paid_deposit_order_msg': self.gui.paid_deposit_order_msg.get("1.0", "end-1c")
                }
                try:
                    response = requests.get("http://localhost:8002/order", params=params)
                    if response.status_code == 200:
                        prefix = self.gui.reply_prefix.get("1.0", "end-1c").format(sender_name=sender_name, order_id=order_id)
                        reply = f"{prefix}\n{response.text}"
                        wechat_instance.send_text(room_wxid, reply.replace('\\n',"\n"))
                except Exception as e:
                    self.logger.error(f"API调用失败: {e}")

    def exit_program(self):
        self.logger.info("正在退出程序...")
        self.exit_flag = True
        keyboard.unhook_all()
        ntwork.exit_()


def main():
    try:
        app = WeChatGUI()
        app.run()
    except Exception as e:
        import traceback
        print(f"程序启动失败: {e}")
        print(traceback.format_exc())
        input("按回车键退出...")


if __name__ == "__main__":
    main()
