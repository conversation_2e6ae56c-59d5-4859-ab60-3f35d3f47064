#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断脚本 - 检查启动监控的问题
"""

import sys
import os
import traceback

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        ('ntwork', 'ntwork'),
        ('tkinter', 'tkinter'),
        ('ttkbootstrap', 'ttkbootstrap'),
        ('python-dotenv', 'dotenv'),
        ('cozepy', 'cozepy'),
    ]
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"  ✅ {package_name} - 已安装")
        except ImportError:
            print(f"  ❌ {package_name} - 未安装")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n⚠️  缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖包都已安装")
    return True

def check_env_file():
    """检查环境变量文件"""
    print("\n🔍 检查环境变量配置...")
    
    env_file = ".env"
    if not os.path.exists(env_file):
        print(f"  ❌ {env_file} 文件不存在")
        print("  请创建 .env 文件并配置以下变量:")
        print("    COZE_JWT_OAUTH_CLIENT_ID=your_client_id")
        print("    COZE_JWT_OAUTH_PRIVATE_KEY=your_private_key")
        print("    COZE_JWT_OAUTH_PUBLIC_KEY_ID=your_public_key_id")
        return False
    
    print(f"  ✅ {env_file} 文件存在")
    
    # 检查环境变量内容
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = [
            'COZE_JWT_OAUTH_CLIENT_ID',
            'COZE_JWT_OAUTH_PRIVATE_KEY', 
            'COZE_JWT_OAUTH_PUBLIC_KEY_ID'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
            else:
                print(f"  ✅ {var} - 已配置")
        
        if missing_vars:
            print(f"  ❌ 缺少环境变量: {', '.join(missing_vars)}")
            return False
            
    except Exception as e:
        print(f"  ❌ 读取环境变量失败: {e}")
        return False
    
    print("✅ 环境变量配置正确")
    return True

def test_coze_workflow():
    """测试Coze工作流"""
    print("\n🔍 测试Coze工作流...")
    
    try:
        from coze.coze_api_key import CozeWorkflow
        
        coze_workflow = CozeWorkflow()
        print("  ✅ CozeWorkflow 实例创建成功")
        
        # 使用示例工作流ID进行测试
        test_workflow_id = "7509123255656087564"  # 这是示例ID，请替换为实际ID
        test_query = "测试连接"
        
        print(f"  🧪 测试工作流ID: {test_workflow_id}")
        response = coze_workflow.run_workflow(test_workflow_id, test_query)
        
        print(f"  ✅ 工作流测试成功")
        print(f"  📝 响应: {response[:100]}...")
        return True
        
    except Exception as e:
        print(f"  ❌ 工作流测试失败: {e}")
        traceback.print_exc()
        return False

def check_config_file():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    config_file = "config.json"
    if os.path.exists(config_file):
        print(f"  ✅ {config_file} 文件存在")
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            workflow_id = config.get('workflow_id', '')
            if workflow_id:
                print(f"  ✅ 配置中的工作流ID: {workflow_id}")
            else:
                print("  ⚠️  配置中没有工作流ID")
                
        except Exception as e:
            print(f"  ❌ 读取配置文件失败: {e}")
    else:
        print(f"  ⚠️  {config_file} 文件不存在（首次运行时正常）")

def main():
    print("🚀 开始诊断启动监控问题...\n")
    
    all_checks_passed = True
    
    # 检查依赖包
    if not check_dependencies():
        all_checks_passed = False
    
    # 检查环境变量
    if not check_env_file():
        all_checks_passed = False
    
    # 检查配置文件
    check_config_file()
    
    # 测试工作流（如果前面的检查都通过）
    if all_checks_passed:
        if not test_coze_workflow():
            all_checks_passed = False
    
    print("\n" + "="*50)
    if all_checks_passed:
        print("✅ 所有检查都通过！")
        print("如果启动监控仍然没有反应，请检查:")
        print("1. 微信是否已登录")
        print("2. 是否已配置监控群列表")
        print("3. 工作流ID是否正确")
        print("4. 查看运行日志选项卡中的错误信息")
    else:
        print("❌ 发现问题，请根据上述提示解决后重试")
    
    print("="*50)

if __name__ == "__main__":
    main()
