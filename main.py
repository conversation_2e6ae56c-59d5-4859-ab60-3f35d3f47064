# -*- coding: utf-8 -*-
import sys, time, re, os
import logging, ntwork
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
from datetime import datetime
import json,ast
import keyboard
import subprocess
import requests
import ttkbootstrap as ttk
from ttkbootstrap import Style
from ttkbootstrap.constants import *  # 导入常量
from scheduler import Scheduler
from scheduler_tab import SchedulerTab
import threading
import ctypes
import random
import asyncio
import concurrent.futures
from queue import Queue, Empty
import gc
import weakref
# 导入coze工作流模块
from coze.coze_api_key import CozeWorkflow
# 导入性能监控模块
from performance_monitor import get_performance_monitor
from performance_config import get_config
# 重定向标准错误输出
class NullWriter:
    def write(self, arg):
        pass
        
# 临时重定向标准错误输出来隐藏libpng警告
old_stderr = sys.stderr
sys.stderr = NullWriter()
# 初始化GUI后恢复标准错误输出
# 在GUI初始化完成后添加: sys.stderr = old_stderr

def singleton_thread_safe(cls):
    _instances = {}
    _lock = threading.Lock()  # 线程锁
    
    def wrapper(*args, **kwargs):
        with _lock:  # 加锁保证线程安全
            if cls not in _instances:
                _instances[cls] = cls(*args, **kwargs)
            return _instances[cls]
    
    return wrapper
# 在文件顶部添加一个全局变量来跟踪实例
_instance = None

# 连接管理器类
class ConnectionManager:
    """管理微信连接状态和自动重连"""
    def __init__(self, logger):
        self.logger = logger
        self.is_connected = False
        self.last_heartbeat = time.time()
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 30  # 重连延迟（秒）
        self.heartbeat_interval = 60  # 心跳检测间隔（秒）
        self.wechat_instance = None
        self.monitor_thread = None
        self.running = False

    def start_monitoring(self, wechat_instance):
        """开始监控连接状态"""
        self.wechat_instance = wechat_instance
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_connection, daemon=True)
        self.monitor_thread.start()
        self.logger.info("连接监控已启动")

    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("连接监控已停止")

    def _monitor_connection(self):
        """监控连接状态的主循环"""
        while self.running:
            try:
                # 检查连接状态
                if self.wechat_instance:
                    try:
                        # 尝试获取登录信息作为心跳检测
                        login_info = self.wechat_instance.get_login_info()
                        if login_info and 'user_id' in login_info:
                            self.is_connected = True
                            self.last_heartbeat = time.time()
                            self.reconnect_attempts = 0
                        else:
                            self.is_connected = False
                    except Exception as e:
                        self.logger.warning(f"心跳检测失败: {e}")
                        self.is_connected = False

                # 如果连接断开，尝试重连
                if not self.is_connected and self.reconnect_attempts < self.max_reconnect_attempts:
                    self.logger.warning(f"检测到连接断开，尝试重连 ({self.reconnect_attempts + 1}/{self.max_reconnect_attempts})")
                    self._attempt_reconnect()

            except Exception as e:
                self.logger.error(f"连接监控异常: {e}")

            time.sleep(self.heartbeat_interval)

    def _attempt_reconnect(self):
        """尝试重连"""
        try:
            self.reconnect_attempts += 1
            time.sleep(self.reconnect_delay)

            if self.wechat_instance:
                # 尝试重新连接
                self.wechat_instance.open(smart=True)
                self.wechat_instance.wait_login(timeout=60)

                # 验证连接
                login_info = self.wechat_instance.get_login_info()
                if login_info and 'user_id' in login_info:
                    self.is_connected = True
                    self.logger.info("重连成功")
                else:
                    self.logger.error("重连失败：无法获取登录信息")

        except Exception as e:
            self.logger.error(f"重连失败: {e}")

# 消息处理队列管理器
class MessageProcessor:
    """异步消息处理器，避免阻塞主线程"""
    def __init__(self, logger, max_workers=3):
        self.logger = logger
        self.message_queue = Queue()
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.running = False
        self.processed_messages = {}  # 消息去重
        self.cleanup_interval = 300  # 5分钟清理一次过期消息记录
        self.last_cleanup = time.time()

    def start(self):
        """启动消息处理器"""
        self.running = True
        # 启动消息处理线程
        threading.Thread(target=self._process_messages, daemon=True).start()
        self.logger.info("消息处理器已启动")

    def stop(self):
        """停止消息处理器"""
        self.running = False
        self.executor.shutdown(wait=True)
        self.logger.info("消息处理器已停止")

    def add_message(self, message, handler, *args, **kwargs):
        """添加消息到处理队列"""
        if not self.running:
            return

        # 消息去重
        message_id = message["data"].get("local_id") or message["data"].get("server_id")
        current_time = time.time()

        if message_id in self.processed_messages:
            if current_time - self.processed_messages[message_id] < 30:
                self.logger.debug(f"忽略重复消息: {message_id}")
                return

        self.processed_messages[message_id] = current_time

        # 添加到队列
        self.message_queue.put((message, handler, args, kwargs))

    def _process_messages(self):
        """处理消息队列"""
        while self.running:
            try:
                # 清理过期的消息记录
                current_time = time.time()
                if current_time - self.last_cleanup > self.cleanup_interval:
                    self._cleanup_processed_messages()
                    self.last_cleanup = current_time

                # 处理队列中的消息
                try:
                    message, handler, args, kwargs = self.message_queue.get(timeout=1)
                    # 提交到线程池异步处理
                    future = self.executor.submit(handler, message, *args, **kwargs)
                    # 不等待结果，避免阻塞
                    self.message_queue.task_done()
                except Empty:
                    continue

            except Exception as e:
                self.logger.error(f"消息处理异常: {e}")

    def _cleanup_processed_messages(self):
        """清理过期的消息记录"""
        current_time = time.time()
        expired_keys = [k for k, v in self.processed_messages.items()
                       if current_time - v > 300]  # 5分钟过期
        for key in expired_keys:
            del self.processed_messages[key]

        if expired_keys:
            self.logger.debug(f"清理了 {len(expired_keys)} 条过期消息记录")

        # 强制垃圾回收
        gc.collect()

# 自定义日志处理器，将日志输出到GUI
class GuiHandler(logging.Handler):
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget

    def emit(self, record):
        try:
            msg = self.format(record)

            def append():
                try:
                    if self.text_widget and self.text_widget.winfo_exists():
                        self.text_widget.configure(state="normal")
                        self.text_widget.insert(tk.END, msg + "\n")
                        self.text_widget.see(tk.END)
                        self.text_widget.configure(state="disabled")
                except tk.TclError:
                    # 如果GUI组件已经被销毁，忽略错误
                    pass

            if self.text_widget:
                self.text_widget.after(0, append)
        except Exception:
            # 如果日志处理失败，不要影响程序运行
            pass

@singleton_thread_safe
class WeChatGUI:
    def __init__(self):
        global _instance
        if _instance is not None:
            raise RuntimeError("WeChatGUI 已经在运行")
        _instance = self

        # 使用 ttkbootstrap 的样式
        self.style = Style(theme='cyborg')  # 使用深色科技风主题
        self.root = self.style.master
        self.root.title("微信Coze机器人")
        self.root.geometry("1350x700")

        # 设置窗口在屏幕中央
        self.center_window()

        # 设置窗口图标（如果有的话）
        # self.root.iconbitmap('path/to/icon.ico')

        self.startup = None

        # 初始化欢迎消息
        self.welcome_message = ""

        # 初始化暂停群列表
        self.paused_rooms = []

        # 创建主框架
        self.create_widgets()
        # 注意：setup_logging 必须在 create_widgets 之后调用，因为需要 log_text 组件
        self.setup_logging()

        # 初始化连接管理器和消息处理器
        self.connection_manager = ConnectionManager(self.logger)
        self.message_processor = MessageProcessor(self.logger)

        # 初始化性能监控器
        self.performance_monitor = get_performance_monitor(self.logger)
        self.performance_monitor.start()

        # 初始化调度器
        self.scheduler = Scheduler(logger=self.logger)

        # 现在可以设置定时任务标签页了
        self.setup_scheduler_tab()

        self.load_config()
        self.init_wechat()

    def __del__(self):
        global _instance
        _instance = None

    def center_window(self):
        """将窗口居中显示"""
        # 获取屏幕的宽度和高度
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 修改窗口尺寸
        window_width = 1350
        window_height = 700
        
        # 计算窗口居中的坐标
        center_x = int((screen_width - window_width) / 2)
        center_y = int((screen_height - window_height) / 2)
        
        # 设置窗口位置
        self.root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")

    def setup_logging(self):
        # 清除现有的处理器，避免重复
        logging.getLogger().handlers.clear()

        # 设置基本配置
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            force=True  # 强制重新配置
        )
        self.logger = logging.getLogger()

        # 确保log_text组件存在后再添加GUI处理器
        if hasattr(self, 'log_text') and self.log_text:
            gui_handler = GuiHandler(self.log_text)
            gui_handler.setFormatter(
                logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
            )
            self.logger.addHandler(gui_handler)

            # 测试日志输出
            self.logger.info("🚀 日志系统初始化完成")
        else:
            print("警告: log_text组件未找到，GUI日志功能可能无法正常工作")

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建群管理选项卡
        self.room_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.room_tab, text="群管理")
        
        # 创建Coze API选项卡
        self.coze_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.coze_tab, text="Coze API")
        
        # 创建日志选项卡
        self.log_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.log_tab, text="运行日志")
        
        # 创建好友管理选项卡
        self.friend_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.friend_tab, text="好友管理")
        
        # 添加定时任务标签页
        self.scheduler_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.scheduler_tab, text="定时任务")
        
        # 设置群管理选项卡内容
        self.setup_room_tab()
        
        # 设置Coze API选项卡内容
        self.setup_coze_tab()
        
        # 设置日志选项卡内容
        self.setup_log_tab()
        
        # 设置好友管理选项卡内容
        self.setup_friend_tab()
        
        # 注意：定时任务标签页的设置将在scheduler初始化后进行
        # 不要在这里调用setup_scheduler_tab()
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame, padding="5")
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动监控", command=self.start_monitoring, bootstyle="success")
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring, state="disabled", bootstyle="danger")
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 添加保存配置按钮
        save_btn = ttk.Button(control_frame, text="保存配置", command=self.save_config, bootstyle="info-outline")
        save_btn.pack(side=tk.LEFT, padx=5)

        # 添加测试日志按钮
        test_log_btn = ttk.Button(control_frame, text="测试日志", command=self.test_logging, bootstyle="warning-outline")
        test_log_btn.pack(side=tk.LEFT, padx=5)

    def setup_room_tab(self):
        # 群列表管理区域
        room_frame = ttk.Frame(self.room_tab, padding="10")
        room_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧群列表
        left_frame = ttk.LabelFrame(room_frame, text="所有群列表", padding="10", bootstyle="primary")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 顶部控制区域
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 搜索框
        search_frame = ttk.Frame(control_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=5)
        self.room_search_entry = ttk.Entry(search_frame)
        self.room_search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.room_search_button = ttk.Button(search_frame, text="搜索", command=self.search_rooms, bootstyle="primary-outline")
        self.room_search_button.pack(side=tk.LEFT, padx=5)
        
        # 刷新按钮
        refresh_btn = ttk.Button(
            control_frame, 
            text="刷新群列表", 
            command=self.refresh_room_list,
            bootstyle="primary-outline"
        )
        refresh_btn.pack(side=tk.RIGHT, padx=5)
        
        # 分页控件
        pagination_frame = ttk.Frame(left_frame)
        pagination_frame.pack(fill=tk.X, pady=5)
        
        self.room_prev_page_btn = ttk.Button(pagination_frame, text="上一页", command=self.prev_room_page, bootstyle="primary-outline")
        self.room_prev_page_btn.pack(side=tk.LEFT, padx=5)
        
        self.room_page_label = ttk.Label(pagination_frame, text="第 1 页")
        self.room_page_label.pack(side=tk.LEFT, padx=5)
        
        self.room_next_page_btn = ttk.Button(pagination_frame, text="下一页", command=self.next_room_page, bootstyle="primary-outline")
        self.room_next_page_btn.pack(side=tk.LEFT, padx=5)
        
        # 创建滚动条和树形视图的框架
        tree_frame = ttk.Frame(left_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.all_rooms_tree = ttk.Treeview(
            tree_frame, 
            columns=('room_id',),
            height=10,
            bootstyle="primary"
        )
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.all_rooms_tree.yview, bootstyle="primary-round")
        self.all_rooms_tree.configure(yscrollcommand=scrollbar.set)
        
        self.all_rooms_tree['show'] = 'tree headings'
        self.all_rooms_tree.heading('#0', text='群名称')
        self.all_rooms_tree.heading('room_id', text='群ID')
        self.all_rooms_tree.column('#0', width=200, stretch=True)
        self.all_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.all_rooms_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 设置群分页相关变量
        self.room_current_page = 1
        self.room_page_size = 500
        self.room_total_pages = 1
        self.room_all_rooms = []
        self.room_search_keyword = ""

        # 中间操作按钮
        btn_frame = ttk.Frame(room_frame)
        btn_frame.pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Button(btn_frame, text="添加 >>", command=self.add_selected_room, bootstyle="primary-outline").pack(pady=5)
        ttk.Button(btn_frame, text="<< 移除", command=self.remove_selected_room, bootstyle="danger-outline").pack(pady=5)
        ttk.Button(btn_frame, text="全选添加 >>", command=self.add_all_rooms, bootstyle="success-outline").pack(pady=5)

        # 右侧监控群列表
        right_frame = ttk.LabelFrame(room_frame, text="监控群列表", padding="10", bootstyle="primary")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 监控群控制区域
        monitor_control_frame = ttk.Frame(right_frame)
        monitor_control_frame.pack(fill=tk.X, pady=(0, 5))

        # 上传监控群按钮
        upload_btn = ttk.Button(
            monitor_control_frame,
            text="上传监控群",
            command=self.upload_monitor_rooms,
            bootstyle="info-outline"
        )
        upload_btn.pack(side=tk.LEFT, padx=5)

        # 清空监控群按钮
        clear_btn = ttk.Button(
            monitor_control_frame,
            text="清空列表",
            command=self.clear_monitor_rooms,
            bootstyle="danger-outline"
        )
        clear_btn.pack(side=tk.LEFT, padx=5)

        # 监控群树形视图
        monitor_frame = ttk.Frame(right_frame)
        monitor_frame.pack(fill=tk.BOTH, expand=True)
        
        self.monitored_rooms_tree = ttk.Treeview(
            monitor_frame,
            columns=('room_id',),
            height=10,
            bootstyle="primary"
        )
        monitor_scrollbar = ttk.Scrollbar(monitor_frame, orient="vertical", command=self.monitored_rooms_tree.yview, bootstyle="primary-round")
        self.monitored_rooms_tree.configure(yscrollcommand=monitor_scrollbar.set)
        
        self.monitored_rooms_tree['show'] = 'tree headings'
        self.monitored_rooms_tree.heading('#0', text='群名称')
        self.monitored_rooms_tree.heading('room_id', text='群ID')
        self.monitored_rooms_tree.column('#0', width=200, stretch=True)
        self.monitored_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.monitored_rooms_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monitor_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def add_all_rooms(self):
        """添加所有群到监控列表"""
        added_count = 0
        for room in self.room_all_rooms:
            try:
                nickname = room.get('nickname', '未知群名')
                conversation_id = room.get('conversation_id', '')
                if not conversation_id:
                    continue
                    
                # 检查是否已经存在
                exists = False
                for existing in self.monitored_rooms_tree.get_children():
                    if self.monitored_rooms_tree.item(existing)['values'][0] == conversation_id:
                        exists = True
                        break
                
                if not exists:
                    # 使用新的ID以避免冲突
                    new_id = f"monitored_{conversation_id}"
                    self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                        text=nickname,  # 显示群名称
                        values=(conversation_id,))  # 保存群ID
                    added_count += 1
            except Exception as e:
                self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                continue
        
        self.logger.info(f"已添加 {added_count} 个群到监控列表")
        messagebox.showinfo("成功", f"已添加 {added_count} 个群到监控列表")

    def setup_coze_tab(self):
        # Coze API配置区域
        coze_frame = ttk.Frame(self.coze_tab, padding="10")
        coze_frame.pack(fill=tk.BOTH, expand=True)

        # API配置
        config_frame = ttk.LabelFrame(coze_frame, text="Coze API配置", padding="10", bootstyle="info")
        config_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)

        # Workflow ID (原来的Bot ID)
        ttk.Label(config_frame, text="Workflow ID:", bootstyle="inverse-info").grid(row=0, column=0, sticky=tk.W, pady=10)
        self.workflow_id_entry = ttk.Entry(config_frame, width=50)
        self.workflow_id_entry.grid(row=0, column=1, padx=5, pady=10, sticky=tk.W)

        # 测试按钮
        test_btn = ttk.Button(config_frame, text="测试工作流", command=self.test_coze_workflow, bootstyle="success-outline")
        test_btn.grid(row=0, column=2, padx=5, pady=10)

        # 高级设置
        advanced_frame = ttk.LabelFrame(coze_frame, text="高级设置", padding="10", bootstyle="secondary")
        advanced_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)

        # 是否需要@机器人
        ttk.Label(advanced_frame, text="需要@机器人才回复:", bootstyle="inverse-secondary").grid(row=0, column=0, sticky=tk.W, pady=10)
        self.require_at_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            advanced_frame,
            variable=self.require_at_var,
            bootstyle="secondary-round-toggle"
        ).grid(row=0, column=1, padx=5, pady=10, sticky=tk.W)

    def test_coze_workflow(self):
        workflow_id = self.workflow_id_entry.get().strip()

        if not workflow_id:
            messagebox.showerror("错误", "Workflow ID不能为空")
            return

        try:
            

            # 测试工作流连接
            test_message = "你好"
            self.logger.info("正在测试工作流连接...workflow_id:" + workflow_id)
            coze_workflow = CozeWorkflow()
            response = coze_workflow.run_workflow(workflow_id, test_message)
            try:
                str_list = ast.literal_eval(response)  # 先转成 Python 列表
                dict_list = [item for item in str_list]  # 再转成字典
                self.logger.info(f"文本回复消息----------{dict_list}")

                for item in dict_list:
                    print(item.get("description", ""), item.get("url", ""))
            except Exception as err:
                self.logger.info(f"文本回复消息----------{err}")
            
        except Exception as e:
            messagebox.showerror("错误", f"工作流连接测试失败: {str(e)}")

    def test_logging(self):
        """测试日志功能"""
        if hasattr(self, 'logger') and self.logger:
            self.logger.info("🧪 这是一条测试日志消息")
            self.logger.warning("⚠️ 这是一条警告日志消息")
            self.logger.error("❌ 这是一条错误日志消息")
            print("日志测试完成 - 请检查运行日志选项卡")
        else:
            print("日志系统未初始化")

    def setup_log_tab(self):
        # 日志显示区
        log_frame = ttk.Frame(self.log_tab, padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = ttk.Text(log_frame, wrap=tk.WORD, height=24)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.configure(state="disabled")
        
        # 添加滚动条
        log_scrollbar = ttk.Scrollbar(self.log_text, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_friend_tab(self):
        # 好友列表管理区域
        self.friends_frame = ttk.Frame(self.friend_tab, padding="10")
        self.friends_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧区域（包含好友列表和欢迎消息）
        left_container = ttk.Frame(self.friends_frame)
        left_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 左侧好友列表
        left_frame = ttk.LabelFrame(left_container, text="所有好友列表", padding="10", bootstyle="primary")
        left_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 顶部控制区域
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 搜索框
        search_frame = ttk.Frame(control_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=5)
        self.search_entry = ttk.Entry(search_frame)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.search_button = ttk.Button(search_frame, text="搜索", command=self.search_friends, bootstyle="primary-outline")
        self.search_button.pack(side=tk.LEFT, padx=5)
        
        # 刷新按钮
        refresh_btn = ttk.Button(
            control_frame, 
            text="刷新好友列表", 
            command=self.refresh_friend_list,
            bootstyle="primary-outline"
        )
        refresh_btn.pack(side=tk.RIGHT, padx=5)
        
        # 分页控件
        pagination_frame = ttk.Frame(left_frame)
        pagination_frame.pack(fill=tk.X, pady=5)
        
        self.prev_page_btn = ttk.Button(pagination_frame, text="上一页", command=self.prev_page, bootstyle="primary-outline")
        self.prev_page_btn.pack(side=tk.LEFT, padx=5)
        
        self.page_label = ttk.Label(pagination_frame, text="第 1/1 页")
        self.page_label.pack(side=tk.LEFT, padx=5)
        
        self.next_page_btn = ttk.Button(pagination_frame, text="下一页", command=self.next_page, bootstyle="primary-outline")
        self.next_page_btn.pack(side=tk.LEFT, padx=5)
        
        # 好友列表树形视图
        friends_tree_frame = ttk.Frame(left_frame)
        friends_tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.all_friends_tree = ttk.Treeview(
            friends_tree_frame, 
            columns=('user_id', 'nickname', 'username'), 
            height=10,
            bootstyle="primary"
        )
        friends_scrollbar = ttk.Scrollbar(friends_tree_frame, orient="vertical", command=self.all_friends_tree.yview, bootstyle="primary-round")
        self.all_friends_tree.configure(yscrollcommand=friends_scrollbar.set)
        
        self.all_friends_tree['show'] = 'headings'
        self.all_friends_tree.heading('user_id', text='用户ID')
        self.all_friends_tree.heading('nickname', text='昵称')
        self.all_friends_tree.heading('username', text='用户名')
        self.all_friends_tree.column('user_id', width=120, stretch=True)
        self.all_friends_tree.column('nickname', width=120, stretch=True)
        self.all_friends_tree.column('username', width=120, stretch=True)
        
        self.all_friends_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        friends_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 分页相关变量
        self.current_page = 1
        self.page_size = 50
        self.total_pages = 1
        self.all_friends = []
        self.search_keyword = ""

        # 添加欢迎消息配置（移到左侧列表下方）
        welcome_frame = ttk.LabelFrame(left_container, text="好友添加欢迎消息", padding="10", bootstyle="info")
        welcome_frame.pack(fill=tk.X, expand=False)
        
        # 欢迎消息输入框
        welcome_input_frame = ttk.Frame(welcome_frame)
        welcome_input_frame.pack(fill=tk.X, expand=True, pady=5)
        
        ttk.Label(welcome_input_frame, text="欢迎消息:", bootstyle="inverse-info").pack(side=tk.LEFT, padx=(0, 5))
        
        self.welcome_message_entry = ttk.Entry(welcome_input_frame, width=30)
        self.welcome_message_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.welcome_message_entry.insert(0, self.welcome_message)
        
        # 添加开启欢迎消息的复选框
        welcome_options_frame = ttk.Frame(welcome_frame)
        welcome_options_frame.pack(fill=tk.X, expand=True, pady=5)
        
        self.enable_welcome_var = tk.BooleanVar(value=False)
        ttk.Label(welcome_options_frame, text="开启欢迎消息:", bootstyle="inverse-info").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(
            welcome_options_frame,
            variable=self.enable_welcome_var,
            bootstyle="info-round-toggle"
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            welcome_input_frame, 
            text="保存", 
            command=self.save_welcome_message,
            bootstyle="info-outline"
        ).pack(side=tk.RIGHT, padx=5)

        # 中间操作按钮
        btn_frame = ttk.Frame(self.friends_frame)
        btn_frame.pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Button(btn_frame, text="添加管理员 >>", command=self.add_selected_friend, bootstyle="primary-outline").pack(pady=5)
        ttk.Button(btn_frame, text="<< 移除", command=self.remove_selected_friend, bootstyle="danger-outline").pack(pady=5)
        ttk.Button(btn_frame, text="添加通知 >>", command=self.add_notify_friend, bootstyle="success-outline").pack(pady=5)
        ttk.Button(btn_frame, text="<< 移除", command=self.remove_notify_friend, bootstyle="danger-outline").pack(pady=5)

        # 右侧分为上下两部分
        right_frame = ttk.Frame(self.friends_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 管理员列表
        admin_frame = ttk.LabelFrame(right_frame, text="管理员列表", padding="10", bootstyle="primary")
        admin_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        admin_tree_frame = ttk.Frame(admin_frame)
        admin_tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.admin_friends_tree = ttk.Treeview(
            admin_tree_frame,
            columns=('user_id', 'nickname', 'username'),
            height=5,
            bootstyle="primary"
        )
        admin_scrollbar = ttk.Scrollbar(admin_tree_frame, orient="vertical", command=self.admin_friends_tree.yview, bootstyle="primary-round")
        self.admin_friends_tree.configure(yscrollcommand=admin_scrollbar.set)
        
        self.admin_friends_tree['show'] = 'tree headings'
        self.admin_friends_tree.heading('#0', text='真实姓名')
        self.admin_friends_tree.heading('user_id', text='用户ID')
        self.admin_friends_tree.heading('nickname', text='昵称')
        self.admin_friends_tree.heading('username', text='用户名')
        self.admin_friends_tree.column('#0', width=120, stretch=True)
        self.admin_friends_tree.column('user_id', width=120, stretch=True)
        self.admin_friends_tree.column('nickname', width=120, stretch=True)
        self.admin_friends_tree.column('username', width=120, stretch=True)
        
        self.admin_friends_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        admin_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 通知列表
        notify_frame = ttk.LabelFrame(right_frame, text="接收通知的好友", padding="10", bootstyle="success")
        notify_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        notify_tree_frame = ttk.Frame(notify_frame)
        notify_tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.notify_friends_tree = ttk.Treeview(
            notify_tree_frame,
            columns=('user_id', 'nickname', 'username'),
            height=5,
            bootstyle="success"
        )
        notify_scrollbar = ttk.Scrollbar(notify_tree_frame, orient="vertical", command=self.notify_friends_tree.yview, bootstyle="success-round")
        self.notify_friends_tree.configure(yscrollcommand=notify_scrollbar.set)
        
        self.notify_friends_tree['show'] = 'tree headings'
        self.notify_friends_tree.heading('#0', text='真实姓名')
        self.notify_friends_tree.heading('user_id', text='用户ID')
        self.notify_friends_tree.heading('nickname', text='昵称')
        self.notify_friends_tree.heading('username', text='用户名')
        self.notify_friends_tree.column('#0', width=120, stretch=True)
        self.notify_friends_tree.column('user_id', width=120, stretch=True)
        self.notify_friends_tree.column('nickname', width=120, stretch=True)
        self.notify_friends_tree.column('username', width=120, stretch=True)
        
        self.notify_friends_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        notify_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def save_welcome_message(self):
        """保存欢迎消息"""
        self.welcome_message = self.welcome_message_entry.get().strip()
   
        
        # 保存到配置文件
        self.save_config()
        
        # 更新 startup 中的欢迎消息
        if self.startup:
            self.startup.welcome_message = self.welcome_message
            self.startup.enable_welcome = self.enable_welcome_var.get()
        
        messagebox.showinfo("成功", "欢迎消息已保存！")

    def init_wechat(self):
        def run():
            try:
                # 确保之前的实例被清理
                if self.startup:
                    try:
                        # 注册空回调替代注销
                        self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
                    except:
                        pass
                    self.startup = None
                    time.sleep(1)  # 等待资源释放
                
                # 初始化新实例
                self.startup = StartUp(self)
                
                try:
                    self.startup.wechat.open(smart=True)
                except Exception as e:
                    self.logger.error(f"打开企业微信失败: {e}")
                    # 尝试重新初始化 ntwork
                    ntwork.exit_()
                    time.sleep(2)
                    self.startup.wechat = ntwork.WeWork()
                    self.startup.wechat.open(smart=True)
                
                self.logger.info("等待登录......")
                self.startup.wechat.wait_login(timeout=500)
                self.logger.info("登录成功，等待数据同步...")
                time.sleep(20)  # 增加等待时间到20秒，让数据更充分同步
                
                # 添加重试机制获取登录信息
                max_retries = 3
                for i in range(max_retries):
                    try:
                        login_info = self.startup.wechat.get_login_info()
                        if login_info and 'user_id' in login_info:
                            self.startup.user_id = login_info["user_id"]
                            if login_info.get("nickname") == '':
                                self.startup.name = login_info.get("username")
                            else:
                                self.startup.name = login_info.get("nickname")
                            self.logger.info(f"登录信息: user_id:{self.startup.user_id}, name:{self.startup.name}")
                            break
                        else:
                            if i < max_retries - 1:
                                self.logger.warning(f"获取登录信息不完整，正在重试... ({i + 1}/{max_retries})")
                                time.sleep(2)  # 等待2秒后重试
                            else:
                                raise RuntimeError("无法获取完整的登录信息")
                    except Exception as e:
                        if i < max_retries - 1:
                            self.logger.warning(f"获取登录信息失败，正在重试... ({i + 1}/{max_retries})")
                            time.sleep(2)
                        else:
                            raise

               #获取群列表
                self.refresh_room_list()
                
                # 获取好友列表
                self.refresh_friend_list()
                
            except Exception as e:
                self.logger.error(f"初始化微信失败: {str(e)}")
                self.logger.error(f"错误详情: ", exc_info=True)
                if self.startup:
                    self.startup.exit_program()
                    self.startup = None
                # 重置按钮状态
                self.root.after(0, self._reset_button_state)

        # 在新线程中初始化微信
        threading.Thread(target=run, daemon=True).start()

    def start_monitoring(self):
        self.logger.info("开始启动监控...")

        global _instance
        if _instance is not self:
            self.logger.error("检测到多个程序实例，请关闭其他窗口")
            return

        # 检查是否已登录
        if not self.startup:
            self.logger.error("微信未初始化，请重启程序")
            return

        self.logger.info("检查微信登录状态...")
        try:
            self.startup.exit_flag = False
            login_info = self.startup.wechat.get_login_info()
            if not login_info:
                self.logger.error("微信未登录，请先登录")
                return
            self.logger.info(f"微信登录状态正常，用户: {login_info.get('nickname', 'Unknown')}")
        except Exception as e:
            self.logger.error(f"获取登录状态失败: {e}")
            return

        # 检查Coze工作流配置
        workflow_id = self.workflow_id_entry.get().strip()
        self.logger.info(f"检查工作流配置，Workflow ID: {workflow_id}")

        if not workflow_id:
            self.logger.error("Workflow ID不能为空，请在Coze API选项卡中配置")
            return


        self.logger.info("保存配置...")
        self.save_config()  # 启动前保存当前配置

        # 设置Coze工作流参数
        try:
            self.logger.info("设置工作流参数...")
            # 直接设置工作流参数
            self.startup.workflow_id = workflow_id
            self.startup.require_at = self.require_at_var.get()

            # 更新暂停群列表
            self.startup.paused_rooms = self.paused_rooms

            # 更新欢迎消息
            self.startup.welcome_message = self.welcome_message
            # 更新是否开启欢迎消息
            self.startup.enable_welcome = self.enable_welcome_var.get()

            # 启动消息处理器
            self.message_processor.start()

            # 启动连接监控
            self.connection_manager.start_monitoring(self.startup.wechat)

            self.logger.info("注册消息回调...")
            # 注册消息回调，使用异步处理
            self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(self._async_message_handler)
            self.logger.info("消息回调注册成功")

            self.logger.info("启动调度器...")
            # 设置调度器的微信实例
            self.scheduler.set_wechat(self.startup.wechat)

            # 启动调度器
            self.scheduler.start()

            # 更新按钮状态
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.logger.info("✅ 监控已成功启动！")

        except Exception as e:
            self.logger.error(f"❌ 启动监控失败: {e}")
            self.logger.error("错误详情: ", exc_info=True)
            # 重置按钮状态
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")

    def _async_message_handler(self, wechat_instance, message):
        """异步消息处理包装器"""
        try:
            # 将消息添加到处理队列，避免阻塞主线程
            self.message_processor.add_message(
                message,
                self.startup.on_recv_message,
                wechat_instance
            )
        except Exception as e:
            self.logger.error(f"消息处理包装器异常: {e}")

    def stop_monitoring(self):
        self.logger.info("开始停止监控...")

        # 停止连接监控
        self.connection_manager.stop_monitoring()

        # 停止消息处理器
        self.message_processor.stop()

        if self.startup:
            # 取消消息回调
            self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
            # 取消好友申请回调
            self.startup.wechat.msg_register(ntwork.MT_RECV_SYS_MSG)(lambda x, y: None)
            # 设置退出标志，确保消息处理循环会检查这个标志
            self.startup.exit_flag = True
            # 清除工作流参数，确保即使有消息进来也不会调用工作流
            if hasattr(self.startup, 'workflow_id'):
                delattr(self.startup, 'workflow_id')

            self.logger.info("消息回调已取消，工作流参数已清除")

        # 在新线程中停止调度器
        threading.Thread(target=self._stop_scheduler, daemon=True).start()

        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.logger.info("监控已停止")

    def _stop_scheduler(self):
        """安全停止调度器"""
        try:
            self.scheduler.stop()
            self.logger.info("调度器已安全停止")
        except Exception as e:
            self.logger.error(f"停止调度器时出错: {e}")

    def run(self):
        try:
            self.root.mainloop()
        finally:
            global _instance
            _instance = None
            if self.startup:
                self.startup.exit_program()

    def load_config(self):
        try:
            if os.path.exists("config.json"):
                with open("config.json", "r", encoding="utf-8") as f:
                    config = json.load(f)
                    
                    # 加载Coze工作流配置
                    self.workflow_id_entry.delete(0, tk.END)
                    self.workflow_id_entry.insert(0, config.get('workflow_id', ''))
                    
                    # 加载高级设置
                    self.require_at_var.set(config.get('require_at', True))
                    
                    # 加载欢迎消息设置
                    self.enable_welcome_var.set(config.get('enable_welcome', False))
                    
                    # 加载欢迎消息
                    self.welcome_message = config.get('welcome_message', "")
                    if hasattr(self, 'welcome_message_entry'):
                        self.welcome_message_entry.delete(0, tk.END)
                        self.welcome_message_entry.insert(0, self.welcome_message)
                    
                    # 加载监控群列表
                    monitored_rooms = config.get('monitored_rooms', [])
                    for room in monitored_rooms:
                        new_id = f"monitored_{room['id']}"
                        self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                            text=room['name'],
                            values=(room['id'],))
                    
                    # 加载暂停群列表
                    self.paused_rooms = config.get('paused_rooms', [])
                    
                    # 加载管理员列表
                    admin_list = config.get('admin_list', [])
                    for admin in admin_list:
                        username = admin.get('username', "")
                        self.admin_friends_tree.insert('', 'end',
                            text=admin['realname'],
                            values=(admin['user_id'], admin['nickname'], username))
                    
                    # 加载通知列表
                    notify_list = config.get('notify_list', [])
                    for notify in notify_list:
                        self.notify_friends_tree.insert('', 'end',
                            text=notify['realname'],
                            values=(notify['user_id'], notify['nickname'], notify['username']))
                    
                    self.logger.info("配置加载成功")
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            # 初始化暂停群列表，确保默认存在
            self.paused_rooms = []

    def save_config(self):
        # 确保 paused_rooms 属性存在
        if not hasattr(self, 'paused_rooms'):
            self.paused_rooms = []
            
        # 确保 welcome_message 属性存在
        if not hasattr(self, 'welcome_message'):
            self.welcome_message = ""
            
        # 确保 enable_welcome_var 属性存在
        if not hasattr(self, 'enable_welcome_var'):
            self.enable_welcome_var = tk.BooleanVar(value=False)

        config = {
            'workflow_id': self.workflow_id_entry.get().strip(),
            'require_at': self.require_at_var.get(),
            'enable_welcome': self.enable_welcome_var.get(),
            'welcome_message': self.welcome_message,
            'paused_rooms': self.paused_rooms,
            'monitored_rooms': [
                {
                    'name': self.monitored_rooms_tree.item(item)['text'],
                    'id': self.monitored_rooms_tree.item(item)['values'][0]
                }
                for item in self.monitored_rooms_tree.get_children()
            ],
            'admin_list': [
                {
                    'realname': self.admin_friends_tree.item(item)['text'],
                    'user_id': self.admin_friends_tree.item(item)['values'][0],
                    'nickname': self.admin_friends_tree.item(item)['values'][1],
                    'username': self.admin_friends_tree.item(item)['values'][2] if len(self.admin_friends_tree.item(item)['values']) > 2 else ""
                }
                for item in self.admin_friends_tree.get_children()
            ],
            'notify_list': [
                {
                    'realname': self.notify_friends_tree.item(item)['text'],
                    'user_id': self.notify_friends_tree.item(item)['values'][0],
                    'nickname': self.notify_friends_tree.item(item)['values'][1],
                    'username': self.notify_friends_tree.item(item)['values'][2] if len(self.notify_friends_tree.item(item)['values']) > 2 else ""
                }
                for item in self.notify_friends_tree.get_children()
            ]
        }
        try:
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            self.logger.info("配置保存成功")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")

    def add_selected_room(self):
        selected = self.all_rooms_tree.selection()
        for item in selected:
            name = self.all_rooms_tree.item(item, 'text')
            room_id = self.all_rooms_tree.item(item, 'values')[0]
            # 检查是否已经存在
            exists = False
            for existing in self.monitored_rooms_tree.get_children():
                if self.monitored_rooms_tree.item(existing)['values'][0] == room_id:
                    exists = True
                    break
            
            if not exists:
                # 使用新的ID以避免冲突
                new_id = f"monitored_{room_id}"
                self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                    text=name,  # 显示群名称
                    values=(room_id,))  # 保存群ID
                self.logger.info(f"添加监控群：{name}")

    def remove_selected_room(self):
        selected = self.monitored_rooms_tree.selection()
        for item in selected:
            self.monitored_rooms_tree.delete(item)

    def search_rooms(self):
        """搜索群列表"""
        self.room_search_keyword = self.room_search_entry.get().strip()
        self.room_current_page = 1
        self.update_rooms_display()

    def prev_room_page(self):
        """上一页群列表"""
        if self.room_current_page > 1:
            self.room_current_page -= 1
            self.update_rooms_display()

    def next_room_page(self):
        """下一页群列表"""
        if self.room_current_page < self.room_total_pages:
            self.room_current_page += 1
            self.update_rooms_display()

    def update_rooms_display(self):
        """更新群列表显示"""
        # 清空当前树形视图中的所有项
        for item in self.all_rooms_tree.get_children():
            self.all_rooms_tree.delete(item)
        
        # 计算分页显示
        filtered_rooms = self.room_all_rooms
        if self.room_search_keyword:
            filtered_rooms = [
                room for room in self.room_all_rooms
                if self.room_search_keyword.lower() in room.get('nickname', '').lower()
            ]
        
        # 计算总页数
        self.room_total_pages = max(1, (len(filtered_rooms) + self.room_page_size - 1) // self.room_page_size)
        
        # 确保当前页在有效范围内
        if self.room_current_page > self.room_total_pages:
            self.room_current_page = self.room_total_pages
        
        # 更新页码显示
        self.room_page_label.config(text=f"第 {self.room_current_page}/{self.room_total_pages} 页")
        
        # 计算当前页应显示的群列表
        start_idx = (self.room_current_page - 1) * self.room_page_size
        end_idx = min(start_idx + self.room_page_size, len(filtered_rooms))
        current_page_rooms = filtered_rooms[start_idx:end_idx]
        
        # 显示当前页的群
        for room in current_page_rooms:
            try:
                nickname = room.get('nickname', '未知群名')
                conversation_id = room.get('conversation_id', '')
                if not conversation_id:
                    continue
                
                self.all_rooms_tree.insert('', 'end', 
                    text=nickname,
                    values=(conversation_id,),
                    tags=('room',))
            except Exception as e:
                self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                continue
        
        self.logger.info(f"当前显示第 {self.room_current_page}/{self.room_total_pages} 页，共 {len(filtered_rooms)} 个群（显示 {len(current_page_rooms)} 个）")

    def refresh_room_list(self):
        if not self.startup or not self.startup.wechat:
            self.logger.error("微信未初始化，请重启程序")
            return
        
        try:
            self.logger.info("正在刷新群列表...")
            
            # 添加重试机制
            max_retries = 3
            self.room_all_rooms = []
            total_rooms = 0
            current_page = 1
            
            while True:
                success = False
                for i in range(max_retries):
                    try:
                        # 带分页参数获取群列表
                        rooms = self.startup.wechat.get_rooms(page_num=current_page, page_size=self.room_page_size)
                        if rooms and 'room_list' in rooms and rooms['room_list']:
                            room_count = len(rooms['room_list'])
                            self.logger.info(f"成功获取第 {current_page} 页群列表，有 {room_count} 个群")
                            self.room_all_rooms.extend(rooms['room_list'])
                            total_rooms += room_count
                            success = True
                            break
                        else:
                            # 如果返回空列表，可能已经获取完所有群
                            if current_page > 1:
                                self.logger.info(f"第 {current_page} 页没有更多群，获取完成")
                                success = True
                                break
                            else:
                                self.logger.warning(f"第 {current_page} 页获取群列表为空，尝试重试... ({i + 1}/{max_retries})")
                                time.sleep(2)
                    except Exception as e:
                        self.logger.warning(f"获取群列表第 {current_page} 页失败: {e}，尝试重试... ({i + 1}/{max_retries})")
                        time.sleep(2)
                
                # 如果当前页获取失败且是第一页，则无法继续
                if not success and current_page == 1:
                    self.logger.error("无法获取群列表，请检查企业微信状态")
                    return
                
                # 如果当前页获取的群数量少于页大小，说明已经没有更多群了
                if success and (len(rooms.get('room_list', [])) < self.room_page_size):
                    self.logger.info(f"已获取所有群，总共 {total_rooms} 个群")
                    break
                
                # 继续获取下一页
                current_page += 1
            
            # 显示第一页
            self.room_current_page = 1
            self.room_search_keyword = ""
            if hasattr(self, 'room_search_entry'):
                self.room_search_entry.delete(0, tk.END)
            self.update_rooms_display()
            
        except Exception as e:
            self.logger.error(f"刷新群列表失败: {str(e)}")
            self.logger.error("错误详情: ", exc_info=True)

    def refresh_friend_list(self, search_keyword=None):
        if not self.startup:
            self.logger.error("微信未初始化，请重启程序")
            return
        
        try:
            self.logger.info("正在刷新好友列表...")
            # 清空现有数据
            self.all_friends = []
            self.search_keyword = search_keyword
            
            # 添加重试机制
            max_retries = 3
            current_page = 1
            total_contacts = 0
            
            # 获取内部联系人
            while True:
                success = False
                for i in range(max_retries):
                    try:
                        inner_contacts = self.startup.wechat.get_inner_contacts(page_num=current_page, page_size=50)
                        if inner_contacts and 'user_list' in inner_contacts and inner_contacts['user_list']:
                            contact_count = len(inner_contacts['user_list'])
                            self.logger.info(f"成功获取内部联系人第 {current_page} 页，共 {contact_count} 个")
                            self.all_friends.extend(inner_contacts['user_list'])
                            total_contacts += contact_count
                            success = True
                            break
                        else:
                            # 如果返回空列表，可能已经获取完所有联系人
                            if current_page > 1:
                                self.logger.info(f"内部联系人第 {current_page} 页没有更多数据，获取完成")
                                success = True
                                break
                            else:
                                self.logger.warning(f"内部联系人第 {current_page} 页为空，尝试重试... ({i + 1}/{max_retries})")
                                time.sleep(2)
                    except Exception as e:
                        if i < max_retries - 1:
                            self.logger.warning(f"获取内部联系人第 {current_page} 页失败: {e}，正在重试... ({i + 1}/{max_retries})")
                            time.sleep(2)
                        else:
                            self.logger.error(f"获取内部联系人失败: {e}")
                
                # 如果当前页获取的联系人数量少于页大小，说明已经没有更多了
                if not success or (success and len(inner_contacts.get('user_list', [])) < 50):
                    break
                
                # 继续获取下一页
                current_page += 1
            
            # 重置页码并获取外部联系人
            current_page = 1
            
            # 获取外部联系人
            while True:
                success = False
                for i in range(max_retries):
                    try:
                        external_contacts = self.startup.wechat.get_external_contacts(page_num=current_page, page_size=50)
                        if external_contacts and 'user_list' in external_contacts and external_contacts['user_list']:
                            contact_count = len(external_contacts['user_list'])
                            self.logger.info(f"成功获取外部联系人第 {current_page} 页，共 {contact_count} 个")
                            self.all_friends.extend(external_contacts['user_list'])
                            total_contacts += contact_count
                            success = True
                            break
                        else:
                            # 如果返回空列表，可能已经获取完所有联系人
                            if current_page > 1:
                                self.logger.info(f"外部联系人第 {current_page} 页没有更多数据，获取完成")
                                success = True
                                break
                            else:
                                self.logger.warning(f"外部联系人第 {current_page} 页为空，尝试重试... ({i + 1}/{max_retries})")
                                time.sleep(2)
                    except Exception as e:
                        if i < max_retries - 1:
                            self.logger.warning(f"获取外部联系人第 {current_page} 页失败: {e}，正在重试... ({i + 1}/{max_retries})")
                            time.sleep(2)
                        else:
                            self.logger.error(f"获取外部联系人失败: {e}")
                
                # 如果当前页获取的联系人数量少于页大小，说明已经没有更多了
                if not success or (success and len(external_contacts.get('user_list', [])) < 50):
                    break
                
                # 继续获取下一页
                current_page += 1
                
            if not self.all_friends:
                self.logger.warning("未获取到任何联系人")
                return
            
            self.logger.info(f"好友列表刷新完成，总共获取 {len(self.all_friends)} 个联系人")
            
            # 重置页码并更新显示
            self.current_page = 1
            self.update_friends_display()
            
        except Exception as e:
            self.logger.error(f"刷新好友列表失败: {str(e)}")
            self.logger.error("错误详情: ", exc_info=True)

    def update_friends_display(self):
        """更新好友列表显示"""
        # 清空当前树形视图中的所有项
        for item in self.all_friends_tree.get_children():
            self.all_friends_tree.delete(item)
        
        # 计算分页显示
        filtered_friends = self.all_friends
        if self.search_keyword:
            search_keyword = self.search_keyword.lower()
            filtered_friends = [
                contact for contact in self.all_friends
                if search_keyword in contact.get('realname', '').lower() or
                   search_keyword in contact.get('nickname', '').lower() or
                   search_keyword in contact.get('user_id', '').lower() or
                   search_keyword in contact.get("username","").lower()
            ]
        # 计算总页数
        self.total_pages = max(1, (len(filtered_friends) + self.page_size - 1) // self.page_size)
        
        # 确保当前页在有效范围内
        if self.current_page > self.total_pages:
            self.current_page = self.total_pages
        
        # 更新页码显示
        self.page_label.config(text=f"第 {self.current_page}/{self.total_pages} 页")
        
        # 计算当前页应显示的好友列表
        start_idx = (self.current_page - 1) * self.page_size
        end_idx = min(start_idx + self.page_size, len(filtered_friends))
        current_page_friends = filtered_friends[start_idx:end_idx]
        
        # 显示当前页的好友
        contact_count = 0
        for contact in current_page_friends:
            try:
                # 确保contact是字典类型
                if not isinstance(contact, dict):
                    self.logger.warning(f"跳过非字典类型的联系人数据: {type(contact)}")
                    continue
                
                realname = contact.get('realname', '')
                nickname = contact.get('nickname', '')
                user_id = contact.get('user_id', '')
                username = contact.get('username', '')
                
                if user_id:  # 只添加有效的联系人
                    self.all_friends_tree.insert('', 'end',
                        text=realname,
                        values=(user_id, nickname, username))
                    contact_count += 1
            except Exception as e:
                self.logger.error(f"处理联系人信息时出错: {e}, 数据: {contact}")
                continue
        
        self.logger.info(f"当前显示第 {self.current_page}/{self.total_pages} 页，共 {len(filtered_friends)} 个联系人（显示 {contact_count} 个）")

    def search_friends(self):
        """搜索好友列表"""
        self.search_keyword = self.search_entry.get().strip()
        self.current_page = 1
        self.update_friends_display()

    def prev_page(self):
        """上一页好友列表"""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_friends_display()

    def next_page(self):
        """下一页好友列表"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.update_friends_display()

    def add_selected_friend(self):
        selected = self.all_friends_tree.selection()
        for item in selected:
            realname = self.all_friends_tree.item(item, 'text')
            values = self.all_friends_tree.item(item, 'values')
            user_id = values[0]
            nickname = values[1]
            username = values[2] if len(values) > 2 else ""
            
            # 检查是否已经存在
            exists = False
            for existing in self.admin_friends_tree.get_children():
                if self.admin_friends_tree.item(existing)['values'][0] == user_id:
                    exists = True
                    break
            
            if not exists:
                self.admin_friends_tree.insert('', 'end',
                    text=realname,
                    values=(user_id, nickname, username))
                self.logger.info(f"添加管理员：{realname} ({username})")

    def remove_selected_friend(self):
        selected = self.admin_friends_tree.selection()
        for item in selected:
            self.admin_friends_tree.delete(item)

    def add_notify_friend(self):
        selected = self.all_friends_tree.selection()
        for item in selected:
            realname = self.all_friends_tree.item(item, 'text')
            values = self.all_friends_tree.item(item, 'values')
            user_id = values[0]
            nickname = values[1]
            username = values[2] if len(values) > 2 else ""
            
            # 检查是否已经存在
            exists = False
            for existing in self.notify_friends_tree.get_children():
                if self.notify_friends_tree.item(existing)['values'][0] == user_id:
                    exists = True
                    break
            
            if not exists:
                self.notify_friends_tree.insert('', 'end',
                    text=realname,
                    values=(user_id, nickname, username))
                self.logger.info(f"添加通知好友：{realname} ({username})")

    def remove_notify_friend(self):
        selected = self.notify_friends_tree.selection()
        for item in selected:
            self.notify_friends_tree.delete(item)

    # 添加重置按钮状态的方法
    def _reset_button_state(self):
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")

    def setup_scheduler_tab(self):
        """设置定时任务标签页"""
        self.scheduler_frame = SchedulerTab(self.scheduler_tab, self.scheduler, self)
        self.scheduler_frame.pack(fill=tk.BOTH, expand=True)

    def upload_monitor_rooms(self):
        """上传监控群txt文件"""
        try:
            # 打开文件对话框选择txt文件
            file_path = filedialog.askopenfilename(
                title="选择监控群列表文件",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if not file_path:
                return

            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                room_names = [line.strip() for line in f.readlines() if line.strip()]

            if not room_names:
                messagebox.showwarning("警告", "文件为空或没有有效的群名称")
                return

            # 批量筛选并添加群
            added_count = 0
            not_found_rooms = []

            for room_name in room_names:
                # 在所有群中查找匹配的群
                found = False
                for room in self.room_all_rooms:
                    if room.get('nickname', '') == room_name:
                        conversation_id = room.get('conversation_id', '')
                        if not conversation_id:
                            continue

                        # 检查是否已经存在于监控列表中
                        exists = False
                        for existing in self.monitored_rooms_tree.get_children():
                            if self.monitored_rooms_tree.item(existing)['values'][0] == conversation_id:
                                exists = True
                                break

                        if not exists:
                            # 添加到监控列表
                            new_id = f"monitored_{conversation_id}"
                            self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                                text=room_name,
                                values=(conversation_id,))
                            added_count += 1
                            self.logger.info(f"添加监控群：{room_name}")

                        found = True
                        break

                if not found:
                    not_found_rooms.append(room_name)

            # 显示结果
            result_msg = f"成功添加 {added_count} 个群到监控列表"
            if not_found_rooms:
                result_msg += f"\n\n未找到以下群：\n" + "\n".join(not_found_rooms[:10])
                if len(not_found_rooms) > 10:
                    result_msg += f"\n... 还有 {len(not_found_rooms) - 10} 个群未找到"

            messagebox.showinfo("上传结果", result_msg)
            self.logger.info(f"批量上传监控群完成，添加了 {added_count} 个群，{len(not_found_rooms)} 个群未找到")

        except Exception as e:
            error_msg = f"上传监控群文件失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)

    def clear_monitor_rooms(self):
        """清空监控群列表"""
        try:
            # 确认对话框
            result = messagebox.askyesno("确认", "确定要清空所有监控群吗？")
            if result:
                # 清空监控群树形视图
                for item in self.monitored_rooms_tree.get_children():
                    self.monitored_rooms_tree.delete(item)

                self.logger.info("已清空监控群列表")
                messagebox.showinfo("成功", "监控群列表已清空")
        except Exception as e:
            error_msg = f"清空监控群列表失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)

@singleton_thread_safe
# 修改StartUp类
class StartUp:
    def __init__(self, gui_instance):
        self.logger = logging.getLogger()
        self.wechat = ntwork.WeWork()
        self.exit_flag = False
        self.user_id = None
        self.name = None
        self.gui = gui_instance
        self.require_at = gui_instance.require_at_var.get()
        # 获取GUI实例中的暂停群列表
        self.paused_rooms = gui_instance.paused_rooms
        # 获取欢迎消息
        self.welcome_message = gui_instance.welcome_message
        # 获取是否开启欢迎消息
        self.enable_welcome = gui_instance.enable_welcome_var.get()
        self.workflow_id = gui_instance.workflow_id_entry.get().strip()
        self.coze_workflow = CozeWorkflow()

    def on_recv_message(self, wechat_instance: ntwork.WeWork, message):
        """优化的消息处理方法，添加超时和错误处理"""
        start_time = time.time()

        try:
            # 使用消息ID来防止重复处理
            message_id = message["data"].get("local_id") or message["data"].get("server_id")
            current_time = time.time()

            # 创建一个类属性来记录已处理的消息
            if not hasattr(self, 'processed_messages'):
                self.processed_messages = {}

            # 如果该消息已处理过且时间间隔小于30秒，则忽略
            if message_id in self.processed_messages:
                if current_time - self.processed_messages[message_id] < 30:
                    self.logger.debug(f"忽略重复接收的消息: {message_id}")
                    return

            # 记录此消息的处理时间
            self.processed_messages[message_id] = current_time

            # 清理过期的消息记录（超过1分钟的）
            if len(self.processed_messages) > 1000:  # 防止内存泄漏
                self.processed_messages = {k: v for k, v in self.processed_messages.items()
                                          if current_time - v < 60}

            # 以下是原有的消息处理逻辑
            # 在方法开始就检查退出标志
            if self.exit_flag:
                return
            # 添加消息接收日志
            self.logger.info(f"收到原始消息: {json.dumps(message, ensure_ascii=False)}")
            self.logger.info(f"机器人名字: {self.name}")
            if not hasattr(self, 'workflow_id'):
                self.logger.error("Coze工作流参数未初始化")
                return
        except Exception as e:
            self.logger.error(f"消息处理异常: {str(e)}", exc_info=True)
        data = message["data"]
        content = data.get("content", "").strip()
        if not content:
            return
        room_wxid = data.get("conversation_id")
        from_wxid = data.get("sender")
        sender_name = data.get("sender_name", "")
        # 检查是否是自己发送的消息
        if from_wxid == self.user_id:
            self.logger.info("忽略自己发送的消息")
            return
        # 获取所有通知好友的会话ID
        notify_users = [
            f"S:{self.user_id}_{self.gui.notify_friends_tree.item(item)['values'][0]}"
            for item in self.gui.notify_friends_tree.get_children()
        ]
        self.logger.info(f"待通知用户列表: {notify_users}")
        if room_wxid=="FILEASSIST":
            return
        self.logger.info(f"消息类型: {data.get('content_type')}")
        # 添加消息类型检查日志
        if data.get("content_type") not in [0,2]:
            self.logger.info(f"忽略非文本消息，类型: {data.get('content_type')}")
            return
        # 添加消息基础信息日志
        self.logger.info(f"消息处理开始 | 发送者: {sender_name}({from_wxid}) | 会话ID: {room_wxid} | 内容: {content}")
        
        # 根据room_wxid前缀判断是群聊还是私聊
        is_private = room_wxid and room_wxid.startswith('S:')
        is_group = room_wxid and room_wxid.startswith('R:')
        
        self.logger.info(f"消息类型: {'私聊' if is_private else '群聊' if is_group else '未知类型'}")
        
        # 如果是群聊，获取机器人在群中的昵称
        if is_group:
            try:
                room_members = wechat_instance.get_room_members(room_wxid)
                for member in room_members['member_list']:
                    if member['username'] == self.name:
                        if member['room_nickname'] == '':
                            self.nickname = self.name
                        else:
                            self.nickname = member['room_nickname']
                        break
            except Exception as e:
                self.logger.warning(f"获取群成员信息失败: {e}")
                self.nickname = self.name
        else:
            # 私聊使用默认昵称
            self.nickname = self.name

        # 获取管理员列表
        admin_list = [
            str(self.gui.admin_friends_tree.item(item)['values'][0]).strip()
            for item in self.gui.admin_friends_tree.get_children()
            if len(self.gui.admin_friends_tree.item(item)['values']) > 0
        ]
        self.logger.info(f"当前管理员列表: {admin_list}")
        
        # 获取监控群列表
        monitored_rooms = [
            self.gui.monitored_rooms_tree.item(item)['values'][0]
            for item in self.gui.monitored_rooms_tree.get_children()
        ]
        self.logger.info(f"当前监控群列表: {monitored_rooms}")
        
        # 处理管理员私聊的恢复命令
        if is_private and from_wxid in admin_list:
            # 恢复指定群的自动回复功能
            if content.startswith("恢复群回复"):
                self.logger.info(f"检测到管理员恢复群回复命令: {content}")
                # 提取群ID
                parts = content.split("恢复群回复", 1)
                if len(parts) > 1:
                    target_room_id = parts[1].strip()
                    self.logger.info(f"管理员 {from_wxid} 请求恢复群 {target_room_id} 的自动回复")
                    
                    # 检查该群是否在暂停列表中
                    if target_room_id in self.gui.paused_rooms:
                        self.gui.paused_rooms.remove(target_room_id)
                        
                        # 在主线程中保存配置
                        # def update_ui():
                        #     self.gui.save_config()
                        
                        # self.gui.root.after(0, update_ui)
                        
                        # 获取群名称
                        room_name = "未知群名"
                        for item in self.gui.all_rooms_tree.get_children():
                            if self.gui.all_rooms_tree.item(item)['values'][0] == target_room_id:
                                room_name = self.gui.all_rooms_tree.item(item)['text']
                                break
                        
                        wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', f"已恢复群 {room_name} 的自动回复\n群ID: {target_room_id}")
                        self.logger.info(f"已恢复群 {room_name} ({target_room_id}) 的自动回复")
                    else:
                        wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', f"群 {target_room_id} 未处于暂停状态")
                else:
                    wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', "请指定要恢复的群ID，格式: 恢复群回复R:xxxx")
                return
            
        # 群聊消息处理
        if is_group:
            self.logger.info(f"群消息检查 | 群ID: {room_wxid} | 是否在监控列表: {room_wxid in monitored_rooms}")
            self.logger.info(f"是否在暂停列表: {room_wxid in self.gui.paused_rooms}")

            # 检查群消息是否在监控列表中
            if room_wxid not in monitored_rooms:
                self.logger.info(f"群 {room_wxid} 不在监控列表中，忽略消息")
                return
                
            # 管理员在监控群中发言，自动暂停该群的自动回复
            if from_wxid in admin_list and room_wxid in monitored_rooms and room_wxid not in self.gui.paused_rooms:
                self.logger.info(f"检测到管理员在监控群中发言，暂停该群自动回复 | 群ID: {room_wxid}")
                
                # 添加到暂停列表
                self.gui.paused_rooms.append(room_wxid)
                
                # 在主线程中保存配置
                # def update_ui():
                #     self.gui.save_config()
                
                # self.gui.root.after(0, update_ui)
                
                # 获取群名称
                room_name = "未知群名"
                for item in self.gui.all_rooms_tree.get_children():
                    if self.gui.all_rooms_tree.item(item)['values'][0] == room_wxid:
                        room_name = self.gui.all_rooms_tree.item(item)['text']
                        break
                    
                # 私聊通知管理员
                wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', 
                    f"检测到您在群 {room_name} 中发言，已自动暂停该群的自动回复\n"
                    f"群ID: {room_wxid}\n"
                    f"恢复命令: 恢复群回复{room_wxid}")
                
                self.logger.info(f"已暂停群 {room_name} ({room_wxid}) 的自动回复")
                return
            
            # 检查群是否在暂停列表中
            if room_wxid in self.gui.paused_rooms:
                self.logger.info(f"群 {room_wxid} 在暂停回复列表中，忽略消息")
                return
        
        
        
        # 检查是否需要@机器人（仅群聊需要）
        is_at_me = False
        if is_group and "@" in content:
            self.logger.info("群聊消息包含@符号，进行检查...")
            if f"@{self.nickname}" in content:
                is_at_me = True
                content = content.replace(f"@{self.nickname}", "").strip()
                self.logger.info(f"检测到@机器人的消息，处理后内容: {content}")
                
        # 群聊中非管理员需要@机器人才回复
        if is_group and self.require_at and not is_at_me:
            if from_wxid in admin_list:
                self.logger.info("群聊中管理员消息无需@，继续处理")
            else:
                self.logger.info("群聊中非管理员消息且未@机器人，已忽略")
                return
                
        try:
            # 生成会话ID，可以使用群ID+发送者ID的组合，私聊直接使用发送者ID
            conversation_id = f"{room_wxid}_{from_wxid}" if is_group else from_wxid
            
            # 工作流调用日志
            self.logger.info(f"调用Coze工作流 | 内容长度: {len(content)} | 会话ID: {conversation_id}")

            response = self.coze_workflow.run_workflow(
                self.workflow_id,
                content
            )
            self.logger.info(f"工作流响应原始数据: {response}")
            # 工作流返回的数据通常已经是处理好的格式，不需要额外处理换行符

            if response.replace("\\n","").strip():

                # 添加回复日志
                self.logger.info(f"准备发送回复 | 内容长度: {len(response)}")

                # 确定发送目标：群聊发送到群，私聊发送到用户
                # 注意：保持原有的发送格式，私聊使用 'S:user_id_from_wxid'
                target_id = room_wxid if is_group else room_wxid  # 私聊时room_wxid已经包含了S:前缀

                try:
                    str_list = ast.literal_eval(response)  # 先转成 Python 列表
                    dict_list = [ast.literal_eval(item) for item in str_list]  # 再转成字典
                    for message in dict_list:
                        wechat_instance.send_link_card(conversation_id=target_id, title=message.get("description", ""),desc=message.get("description", ""),url=message.get('url'),image_url=message.get('url'))
                        time.sleep(random.uniform(0.5, 2))
                except Exception:
                    # 直接发送回复
                    wechat_instance.send_text(target_id, response.replace('[]',''))

                self.logger.info(f"消息回复成功，发送至: {target_id}")
            else:
                # 添加回复日志
                self.logger.info("没有回复内容")
                # 获取所有通知好友的会话ID
                notify_users = [
                    f"S:{self.user_id}_{self.gui.notify_friends_tree.item(item)['values'][0]}"
                    for item in self.gui.notify_friends_tree.get_children()
                ]
                if notify_users:
                    wechat_instance.send_text(conversation_id=random.choice(notify_users),
                                              content=f"===机器人无法回复，请注意查看===\n发送者:\n {sender_name}({from_wxid}) \n 会话ID: {room_wxid} \n 内容: {content}"
                                              )

        except Exception as e:
            # 外层异常处理
            processing_time = time.time() - start_time
            self.logger.error(f"消息处理失败 (耗时: {processing_time:.2f}s): {str(e)}", exc_info=True)

            # 如果是网络相关错误，记录连接状态
            if "network" in str(e).lower() or "connection" in str(e).lower():
                self.logger.warning("检测到网络连接问题，可能需要重连")

        finally:
            # 性能监控
            processing_time = time.time() - start_time
            if processing_time > 10:  # 如果处理时间超过10秒
                self.logger.warning(f"消息处理耗时过长: {processing_time:.2f}s")

            # 记录性能数据
            if hasattr(self, 'gui') and hasattr(self.gui, 'performance_monitor'):
                self.gui.performance_monitor.record_operation("message_processing", processing_time)
                self.gui.performance_monitor.record_message()

            # 定期清理内存
            if hasattr(self, 'processed_messages') and len(self.processed_messages) > 500:
                current_time = time.time()
                self.processed_messages = {k: v for k, v in self.processed_messages.items()
                                          if current_time - v < 300}  # 保留5分钟内的记录
                gc.collect()  # 强制垃圾回收

    def exit_program(self):
        self.logger.info("正在退出程序...")
        self.exit_flag = True
        keyboard.unhook_all()
        ntwork.exit_()


def main():
    try:
        app = WeChatGUI()
        app.run()
    except Exception as e:
        import traceback
        print(f"程序启动失败: {e}")
        print(traceback.format_exc())
        input("按回车键退出...")


if __name__ == "__main__":
    main() 