import time
from cozepy import Co<PERSON>, <PERSON><PERSON><PERSON>uth, JWTOAuth<PERSON>pp, COZE_CN_BASE_URL


class TokenManager:
    def __init__(self, client_id: str, private_key: str, public_key_id: str):
        self.client_id = client_id
        self.private_key = private_key
        self.public_key_id = public_key_id
        self._access_token = None
        self._token_expires_at = 0

        self.jwt_oauth_app = JWTOAuthApp(
            client_id=self.client_id,
            private_key=self.private_key,
            public_key_id=public_key_id,
            base_url=COZE_CN_BASE_URL
        )

    def get_token(self) -> str:
        """获取有效的 token，如果过期则自动刷新"""
        current_time = time.time()

        # 如果 token 不存在或即将过期（预留 60s 缓冲），则刷新
        if not self._access_token or current_time >= (self._token_expires_at - 60):
            self._refresh_token()

        return self._access_token

    def _refresh_token(self):
        """刷新 token"""
        try:
            oauth_token = self.jwt_oauth_app.get_access_token(ttl=3600)
            self._access_token = oauth_token.access_token
            # 设置过期时间
            self._token_expires_at = time.time() + 3600
        except Exception as e:
            print(f"刷新 token 失败: {str(e)}")
            raise

    def get_coze_client(self) -> Coze:
        """获取配置好的 Coze 客户端"""
        token = self.get_token()
        return Coze(
            auth=TokenAuth(token),
            base_url=COZE_CN_BASE_URL
        )