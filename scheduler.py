# -*- coding: utf-8 -*-
import threading
import time
import datetime
import json
import logging
import os
from queue import Queue, Empty

class SchedulerTask:
    def __init__(self, task_id, target_id, message, schedule_time, repeat_type='once', repeat_value=None):
        """
        初始化定时任务
        :param task_id: 任务ID
        :param target_id: 目标ID (群ID或私聊ID)，可以是单个ID字符串或ID列表
        :param message: 要发送的消息
        :param schedule_time: 计划执行时间 (datetime对象)
        :param repeat_type: 重复类型 ('once', 'daily', 'weekly', 'monthly')
        :param repeat_value: 重复值 (对于weekly是星期几0-6，monthly是日期1-31)
        """
        self.task_id = task_id
        # 确保 target_id 总是列表形式，即使只有一个目标
        self.target_id = target_id if isinstance(target_id, list) else [target_id]
        self.message = message
        self.schedule_time = schedule_time
        self.repeat_type = repeat_type
        self.repeat_value = repeat_value
        self.enabled = True
        self.last_run = None
    
    def to_dict(self):
        """将任务转换为字典，用于保存"""
        return {
            'task_id': self.task_id,
            'target_id': self.target_id,
            'message': self.message,
            'schedule_time': self.schedule_time.strftime('%Y-%m-%d %H:%M:%S'),
            'repeat_type': self.repeat_type,
            'repeat_value': self.repeat_value,
            'enabled': self.enabled,
            'last_run': self.last_run.strftime('%Y-%m-%d %H:%M:%S') if self.last_run else None
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建任务，用于加载"""
        task = cls(
            data['task_id'],
            data['target_id'],
            data['message'],
            datetime.datetime.strptime(data['schedule_time'], '%Y-%m-%d %H:%M:%S'),
            data.get('repeat_type', 'once'),
            data.get('repeat_value')
        )
        task.enabled = data.get('enabled', True)
        if data.get('last_run'):
            task.last_run = datetime.datetime.strptime(data['last_run'], '%Y-%m-%d %H:%M:%S')
        return task
    
    def should_run_now(self):
        """检查任务是否应该现在运行"""
        if not self.enabled:
            return False
            
        now = datetime.datetime.now()
        
        # 对于一次性任务，检查是否到达计划时间
        if self.repeat_type == 'once':
            return now >= self.schedule_time
        
        # 对于重复任务，检查上次运行时间和当前时间
        if self.last_run is None:
            # 首次运行，检查是否到达计划时间
            if now < self.schedule_time:
                return False
        else:
            # 已经运行过，检查是否应该再次运行
            if self.repeat_type == 'daily':
                # 检查是否是新的一天的同一时间
                next_run = datetime.datetime.combine(
                    self.last_run.date() + datetime.timedelta(days=1),
                    self.schedule_time.time()
                )
                return now >= next_run
            
            elif self.repeat_type == 'weekly':
                # 检查是否是新的一周的同一天同一时间
                days_ahead = self.repeat_value - self.last_run.weekday()
                if days_ahead <= 0:  # 目标星期已过，等待下周
                    days_ahead += 7
                next_run = datetime.datetime.combine(
                    self.last_run.date() + datetime.timedelta(days=days_ahead),
                    self.schedule_time.time()
                )
                return now >= next_run
            
            elif self.repeat_type == 'monthly':
                # 检查是否是新的一月的同一日期同一时间
                next_month = self.last_run.month + 1
                next_year = self.last_run.year
                if next_month > 12:
                    next_month = 1
                    next_year += 1
                
                # 处理月末日期问题
                day = min(self.repeat_value, 
                         [31, 29 if (next_year % 4 == 0 and next_year % 100 != 0) or next_year % 400 == 0 else 28, 
                          31, 30, 31, 30, 31, 31, 30, 31, 30, 31][next_month-1])
                
                next_run = datetime.datetime(
                    next_year, next_month, day,
                    self.schedule_time.hour, self.schedule_time.minute, self.schedule_time.second
                )
                return now >= next_run
        
        # 默认情况
        return False
    
    def update_next_run(self):
        """更新任务的下一次运行时间"""
        self.last_run = datetime.datetime.now()
        
        # 对于一次性任务，禁用它
        if self.repeat_type == 'once':
            self.enabled = False


class Scheduler:
    def __init__(self, wechat_instance=None, logger=None):
        self.tasks = {}  # 任务字典，键为任务ID
        self.wechat = wechat_instance
        self.logger = logger or logging.getLogger()
        self.running = False
        self.thread = None
        self.task_queue = Queue()
        self.lock = threading.Lock()
        self.config_file = 'scheduler_tasks.json'
        self.load_tasks()
    
    def set_wechat(self, wechat_instance):
        """设置微信实例"""
        self.wechat = wechat_instance
    
    def add_task(self, task):
        """添加新任务"""
        with self.lock:
            self.tasks[task.task_id] = task
            self.save_tasks()
            self.logger.info(f"添加定时任务: {task.task_id}, 目标: {task.target_id}, 时间: {task.schedule_time}")
    
    def remove_task(self, task_id):
        """移除任务"""
        with self.lock:
            if task_id in self.tasks:
                del self.tasks[task_id]
                self.save_tasks()
                self.logger.info(f"移除定时任务: {task_id}")
                return True
            return False
    
    def update_task(self, task):
        """更新任务"""
        with self.lock:
            if task.task_id in self.tasks:
                self.tasks[task.task_id] = task
                self.save_tasks()
                self.logger.info(f"更新定时任务: {task.task_id}")
                return True
            return False
    
    def get_task(self, task_id):
        """获取任务"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self):
        """获取所有任务"""
        return list(self.tasks.values())
    
    def save_tasks(self):
        """保存任务到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump({tid: task.to_dict() for tid, task in self.tasks.items()}, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存定时任务失败: {e}")
    
    def load_tasks(self):
        """从文件加载任务"""
        if not os.path.exists(self.config_file):
            return
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                for tid, task_data in data.items():
                    self.tasks[tid] = SchedulerTask.from_dict(task_data)
            self.logger.info(f"已加载 {len(self.tasks)} 个定时任务")
        except Exception as e:
            self.logger.error(f"加载定时任务失败: {e}")
    
    def start(self):
        """启动调度器"""
        if self.running:
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._run, daemon=True)
        self.thread.start()
        self.logger.info("定时任务调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1)
            self.thread = None
        self.logger.info("定时任务调度器已停止")
    
    def _run(self):
        """调度器主循环"""
        while self.running:
            now = datetime.datetime.now()
            
            # 检查所有任务
            with self.lock:
                for task_id, task in list(self.tasks.items()):
                    if task.should_run_now():
                        # 将任务放入队列
                        self.task_queue.put(task)
                        task.update_next_run()
                        self.save_tasks()
            
            # 执行队列中的任务
            try:
                while True:
                    task = self.task_queue.get_nowait()
                    self._execute_task(task)
                    self.task_queue.task_done()
            except Empty:
                pass
            
            # 每10秒检查一次
            time.sleep(10)
    
    def _execute_task(self, task):
        """执行任务"""
        if not self.wechat:
            self.logger.error(f"无法执行任务 {task.task_id}: 微信实例未设置")
            return
            
        try:
            self.logger.info(f"执行定时任务: {task.task_id}, 目标数量: {len(task.target_id)}")
            
            # 遍历所有目标，发送消息
            success_count = 0
            for target in task.target_id:
                try:
                    self.wechat.send_text(target, task.message)
                    success_count += 1
                except Exception as e:
                    self.logger.error(f"向目标 {target} 发送消息失败: {e}")
            
            self.logger.info(f"定时任务 {task.task_id} 执行完成，成功发送 {success_count}/{len(task.target_id)} 条消息")
        except Exception as e:
            self.logger.error(f"执行定时任务 {task.task_id} 失败: {e}") 