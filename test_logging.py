#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志功能
"""

import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap import Style
import logging
import time

# 自定义日志处理器，将日志输出到GUI
class GuiHandler(logging.Handler):
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget

    def emit(self, record):
        try:
            msg = self.format(record)

            def append():
                try:
                    if self.text_widget and self.text_widget.winfo_exists():
                        self.text_widget.configure(state="normal")
                        self.text_widget.insert(tk.END, msg + "\n")
                        self.text_widget.see(tk.END)
                        self.text_widget.configure(state="disabled")
                except tk.TclError:
                    # 如果GUI组件已经被销毁，忽略错误
                    pass

            if self.text_widget:
                self.text_widget.after(0, append)
        except Exception:
            # 如果日志处理失败，不要影响程序运行
            pass

class LogTestApp:
    def __init__(self):
        # 使用 ttkbootstrap 的样式
        self.style = Style(theme='cyborg')
        self.root = self.style.master
        self.root.title("日志测试")
        self.root.geometry("800x600")
        
        self.create_widgets()
        self.setup_logging()
        
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建日志显示区
        log_frame = ttk.LabelFrame(main_frame, text="日志输出", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.log_text = ttk.Text(log_frame, wrap=tk.WORD, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.configure(state="disabled")
        
        # 添加滚动条
        log_scrollbar = ttk.Scrollbar(self.log_text, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建按钮区
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="测试INFO日志", command=self.test_info).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="测试WARNING日志", command=self.test_warning).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="测试ERROR日志", command=self.test_error).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
    def setup_logging(self):
        # 清除现有的处理器，避免重复
        logging.getLogger().handlers.clear()
        
        # 设置基本配置
        logging.basicConfig(
            level=logging.INFO, 
            format="%(asctime)s - %(levelname)s - %(message)s",
            force=True  # 强制重新配置
        )
        self.logger = logging.getLogger()

        # 确保log_text组件存在后再添加GUI处理器
        if hasattr(self, 'log_text') and self.log_text:
            gui_handler = GuiHandler(self.log_text)
            gui_handler.setFormatter(
                logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
            )
            self.logger.addHandler(gui_handler)
            
            # 测试日志输出
            self.logger.info("🚀 日志系统初始化完成")
        else:
            print("警告: log_text组件未找到，GUI日志功能可能无法正常工作")
    
    def test_info(self):
        self.logger.info("这是一条INFO级别的测试日志")
        
    def test_warning(self):
        self.logger.warning("这是一条WARNING级别的测试日志")
        
    def test_error(self):
        self.logger.error("这是一条ERROR级别的测试日志")
        
    def clear_log(self):
        self.log_text.configure(state="normal")
        self.log_text.delete(1.0, tk.END)
        self.log_text.configure(state="disabled")
        self.logger.info("日志已清空")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = LogTestApp()
    app.run()
