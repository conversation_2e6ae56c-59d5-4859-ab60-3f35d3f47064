import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import datetime
import time
from scheduler import SchedulerTask
import ttkbootstrap as ttk  # 使用ttkbootstrap替代标准ttk

class SchedulerTab(ttk.Frame):
    def __init__(self, parent, scheduler, wechat_gui):
        super().__init__(parent, padding=10)
        self.scheduler = scheduler
        self.wechat_gui = wechat_gui
        self.logger = wechat_gui.logger
        
        # 创建界面
        self.create_widgets()
        
        # 加载任务列表
        self.refresh_scheduler_tasks()
        
    def create_widgets(self):
        """创建界面元素"""
        # 左侧任务列表
        left_frame = ttk.LabelFrame(self, text="定时任务列表", padding="10", bootstyle="primary")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 创建滚动条和树形视图的框架
        tree_frame = ttk.Frame(left_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.tasks_tree = ttk.Treeview(
            tree_frame, 
            columns=('target', 'time', 'repeat', 'status'),
            height=10,
            bootstyle="primary"
        )
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tasks_tree.yview, bootstyle="primary-round")
        self.tasks_tree.configure(yscrollcommand=scrollbar.set)
        
        self.tasks_tree['show'] = 'headings'
        self.tasks_tree.heading('target', text='目标')
        self.tasks_tree.heading('time', text='时间')
        self.tasks_tree.heading('repeat', text='重复')
        self.tasks_tree.heading('status', text='状态')
        self.tasks_tree.column('target', width=150, stretch=True)
        self.tasks_tree.column('time', width=150, stretch=True)
        self.tasks_tree.column('repeat', width=100, stretch=True)
        self.tasks_tree.column('status', width=80, stretch=True)
        
        self.tasks_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.tasks_tree.bind('<<TreeviewSelect>>', self.on_task_select)
        
        # 按钮区域
        btn_frame = ttk.Frame(left_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_frame, text="添加任务", command=self.add_scheduler_task, bootstyle="success").pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="编辑任务", command=self.edit_scheduler_task, bootstyle="info").pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除任务", command=self.delete_scheduler_task, bootstyle="danger").pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="刷新列表", command=self.refresh_scheduler_tasks, bootstyle="secondary").pack(side=tk.LEFT, padx=5)
        
        # 右侧任务详情
        right_frame = ttk.LabelFrame(self, text="任务详情", padding="10", bootstyle="primary")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 目标选择
        ttk.Label(right_frame, text="发送目标:", bootstyle="inverse-primary").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.task_target_type = ttk.Combobox(right_frame, values=["群聊", "私聊"], state="readonly", width=10)
        self.task_target_type.current(0)
        self.task_target_type.grid(row=0, column=1, sticky=tk.W, pady=5)
        self.task_target_type.bind("<<ComboboxSelected>>", self.on_target_type_change)

        # 上传按钮区域
        upload_btn_frame = ttk.Frame(right_frame)
        upload_btn_frame.grid(row=0, column=2, columnspan=2, sticky=tk.E, pady=5, padx=(10, 0))

        self.upload_groups_btn = ttk.Button(
            upload_btn_frame,
            text="上传监控群",
            command=self.upload_monitor_groups,
            bootstyle="info-outline",
            width=12
        )
        self.upload_groups_btn.pack(side=tk.LEFT, padx=2)

        self.upload_friends_btn = ttk.Button(
            upload_btn_frame,
            text="上传监控好友",
            command=self.upload_monitor_friends,
            bootstyle="success-outline",
            width=12
        )
        self.upload_friends_btn.pack(side=tk.LEFT, padx=2)
        
        # 修改为多选列表框
        target_frame = ttk.Frame(right_frame)
        target_frame.grid(row=1, column=0, columnspan=4, sticky=tk.W+tk.E, pady=5)
        
        # 创建目标选择列表框和滚动条
        self.targets_listbox = tk.Listbox(target_frame, selectmode=tk.MULTIPLE, height=6, width=50)
        target_scrollbar = ttk.Scrollbar(target_frame, orient="vertical", command=self.targets_listbox.yview)
        self.targets_listbox.configure(yscrollcommand=target_scrollbar.set)
        
        self.targets_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        target_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 消息内容
        ttk.Label(right_frame, text="消息内容:", bootstyle="inverse-primary").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.task_message = tk.Text(right_frame, width=40, height=5)  # 使用tk.Text
        self.task_message.grid(row=2, column=1, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        # 时间设置
        ttk.Label(right_frame, text="执行时间:", bootstyle="inverse-primary").grid(row=3, column=0, sticky=tk.W, pady=5)
        time_frame = ttk.Frame(right_frame)
        time_frame.grid(row=3, column=1, columnspan=3, sticky=tk.W, pady=5)
        
        # 使用简单的日期输入替代DateEntry
        date_frame = ttk.Frame(time_frame)
        date_frame.pack(side=tk.LEFT, padx=5)
        
        # 年月日输入
        today = datetime.datetime.now()
        self.year_var = tk.StringVar(value=str(today.year))
        self.month_var = tk.StringVar(value=str(today.month))
        self.day_var = tk.StringVar(value=str(today.day))
        
        ttk.Label(date_frame, text="日期:").pack(side=tk.LEFT)
        ttk.Entry(date_frame, textvariable=self.year_var, width=5).pack(side=tk.LEFT)
        ttk.Label(date_frame, text="-").pack(side=tk.LEFT)
        ttk.Entry(date_frame, textvariable=self.month_var, width=3).pack(side=tk.LEFT)
        ttk.Label(date_frame, text="-").pack(side=tk.LEFT)
        ttk.Entry(date_frame, textvariable=self.day_var, width=3).pack(side=tk.LEFT)
        
        # 时间选择
        hour_frame = ttk.Frame(time_frame)
        hour_frame.pack(side=tk.LEFT, padx=5)
        
        self.task_hour = ttk.Spinbox(hour_frame, from_=0, to=23, width=3, format="%02.0f")
        self.task_hour.set(datetime.datetime.now().hour)
        self.task_hour.pack(side=tk.LEFT)
        
        ttk.Label(hour_frame, text=":").pack(side=tk.LEFT)
        
        self.task_minute = ttk.Spinbox(hour_frame, from_=0, to=59, width=3, format="%02.0f")
        self.task_minute.set(datetime.datetime.now().minute)
        self.task_minute.pack(side=tk.LEFT)
        
        # 重复设置
        ttk.Label(right_frame, text="重复规则:", bootstyle="inverse-primary").grid(row=4, column=0, sticky=tk.W, pady=5)
        repeat_frame = ttk.Frame(right_frame)
        repeat_frame.grid(row=4, column=1, columnspan=3, sticky=tk.W, pady=5)
        
        self.task_repeat_type = ttk.Combobox(repeat_frame, values=["一次性", "每天", "每周", "每月"], state="readonly", width=10)
        self.task_repeat_type.current(0)
        self.task_repeat_type.pack(side=tk.LEFT, padx=5)
        self.task_repeat_type.bind("<<ComboboxSelected>>", self.on_repeat_type_change)
        
        # 每周选择框架
        self.week_frame = ttk.Frame(repeat_frame)
        
        self.week_day_var = tk.StringVar(value="1")  # 默认周一
        week_days = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        self.week_day_combo = ttk.Combobox(self.week_frame, values=week_days, state="readonly", width=10, textvariable=self.week_day_var)
        self.week_day_combo.current(0)
        self.week_day_combo.pack(side=tk.LEFT, padx=5)
        
        # 每月选择框架
        self.month_frame = ttk.Frame(repeat_frame)
        
        self.month_day_var = tk.StringVar(value="1")  # 默认1号
        month_days = [str(i) for i in range(1, 32)]
        self.month_day_combo = ttk.Combobox(self.month_frame, values=month_days, state="readonly", width=5, textvariable=self.month_day_var)
        self.month_day_combo.current(0)
        self.month_day_combo.pack(side=tk.LEFT, padx=5)
        ttk.Label(self.month_frame, text="日").pack(side=tk.LEFT)
        
        # 按钮区域
        button_frame = ttk.Frame(right_frame)
        button_frame.grid(row=5, column=0, columnspan=4, pady=10)
        
        ttk.Button(button_frame, text="保存任务", command=self.save_task, bootstyle="success").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空表单", command=self.clear_form, bootstyle="warning").pack(side=tk.LEFT, padx=5)
        
        # 初始化界面状态
        self.on_target_type_change(None)
        self.on_repeat_type_change(None)
        
    def on_target_type_change(self, event):
        """目标类型变更处理"""
        target_type = self.task_target_type.get()
        
        # 清空目标列表
        self.targets_listbox.delete(0, tk.END)
        
        if target_type == "群聊":
            # 加载群列表
            for item in self.wechat_gui.all_rooms_tree.get_children():
                room_name = self.wechat_gui.all_rooms_tree.item(item)['text']
                room_id = self.wechat_gui.all_rooms_tree.item(item)['values'][0]
                self.targets_listbox.insert(tk.END, f"{room_name} ({room_id})")
        else:
            # 加载好友列表
            for item in self.wechat_gui.all_friends_tree.get_children():
                realname = self.wechat_gui.all_friends_tree.item(item)['text']
                values = self.wechat_gui.all_friends_tree.item(item)['values']
                user_id = values[0]
                username = values[2] if len(values) > 2 else ""
                
                display = f"{realname}"
                if username:
                    display += f"[{username}]"
                display += f"({user_id})"
                
                self.targets_listbox.insert(tk.END, display)
    
    def on_repeat_type_change(self, event):
        """重复类型变更处理"""
        repeat_type = self.task_repeat_type.get()
        
        # 隐藏所有特殊设置框架
        self.week_frame.pack_forget()
        self.month_frame.pack_forget()
        
        # 根据重复类型显示对应设置
        if repeat_type == "每周":
            self.week_frame.pack(side=tk.LEFT)
        elif repeat_type == "每月":
            self.month_frame.pack(side=tk.LEFT)
    
    def on_task_select(self, event):
        """任务选择处理"""
        selected = self.tasks_tree.selection()
        if not selected:
            return
            
        task_id = selected[0]
        task = self.scheduler.get_task(task_id)
        if not task:
            return
            
        # 填充表单
        self.clear_form()
        
        # 设置目标类型和目标
        is_group = any(target_id.startswith('R:') for target_id in task.target_id)
        if is_group:
            self.task_target_type.set("群聊")
        else:
            self.task_target_type.set("私聊")
        
        self.on_target_type_change(None)  # 刷新目标列表
        
        # 查找并选择目标
        target_list = list(self.targets_listbox.get(0, tk.END))
        for target_id in task.target_id:
            for i, target_text in enumerate(target_list):
                if f"({target_id})" in target_text:
                    self.targets_listbox.selection_set(i)
        
        # 设置消息内容
        self.task_message.delete('1.0', tk.END)
        self.task_message.insert('1.0', task.message)
        
        # 设置日期和时间
        self.year_var.set(task.schedule_time.strftime('%Y'))
        self.month_var.set(task.schedule_time.strftime('%m'))
        self.day_var.set(task.schedule_time.strftime('%d'))
        self.task_hour.set(task.schedule_time.hour)
        self.task_minute.set(task.schedule_time.minute)
        
        # 设置重复类型
        repeat_map = {
            'once': "一次性",
            'daily': "每天",
            'weekly': "每周",
            'monthly': "每月"
        }
        self.task_repeat_type.set(repeat_map.get(task.repeat_type, "一次性"))
        self.on_repeat_type_change(None)  # 刷新重复设置界面
        
        # 设置重复值
        if task.repeat_type == 'weekly' and task.repeat_value is not None:
            self.week_day_var.set(task.repeat_value)
        elif task.repeat_type == 'monthly' and task.repeat_value is not None:
            self.month_day_var.set(task.repeat_value)
    
    def add_scheduler_task(self):
        """添加新任务"""
        self.clear_form()
        self.tasks_tree.selection_clear()
    
    def edit_scheduler_task(self):
        """编辑任务"""
        selected = self.tasks_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个任务")
            return
    
    def delete_scheduler_task(self):
        """删除任务"""
        selected = self.tasks_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个任务")
            return
            
        if messagebox.askyesno("确认", "确定要删除选中的任务吗？"):
            task_id = selected[0]
            if self.scheduler.remove_task(task_id):
                self.refresh_scheduler_tasks()
                messagebox.showinfo("成功", "任务已删除")
            else:
                messagebox.showerror("错误", "删除任务失败")
    
    def refresh_scheduler_tasks(self):
        """刷新任务列表"""
        # 清空列表
        for item in self.tasks_tree.get_children():
            self.tasks_tree.delete(item)
            
        # 加载任务
        tasks = self.scheduler.get_all_tasks()
        for task in tasks:
            # 获取目标名称
            target_names = []
            for target_id in task.target_id:
                target_name = self._get_target_name(target_id)
                if target_name:
                    target_names.append(target_name)
            
            target_display = ", ".join(target_names) if target_names else "未知目标"
            if len(target_display) > 30:
                target_display = f"{target_display[:27]}... ({len(task.target_id)}个)"
            
            # 格式化时间
            time_str = task.schedule_time.strftime('%Y-%m-%d %H:%M')
            
            # 格式化重复类型
            repeat_map = {
                'once': "一次性",
                'daily': "每天",
                'weekly': "每周",
                'monthly': "每月"
            }
            repeat_str = repeat_map.get(task.repeat_type, "未知")
            
            # 状态
            status = "启用" if task.enabled else "禁用"
            
            # 插入数据
            self.tasks_tree.insert('', 'end', iid=task.task_id,
                values=(target_display, time_str, repeat_str, status))
    
    def _get_target_name(self, target_id):
        """获取目标名称"""
        # 群聊
        if target_id.startswith('R:'):
            for item in self.wechat_gui.all_rooms_tree.get_children():
                if self.wechat_gui.all_rooms_tree.item(item)['values'][0] == target_id:
                    return self.wechat_gui.all_rooms_tree.item(item)['text']
                    
        # 私聊
        elif target_id.startswith('S:'):
            # 提取用户ID
            parts = target_id.split('_')
            if len(parts) > 1:
                user_id = parts[1]
                for item in self.wechat_gui.all_friends_tree.get_children():
                    values = self.wechat_gui.all_friends_tree.item(item)['values']
                    if values[0] == user_id:
                        realname = self.wechat_gui.all_friends_tree.item(item)['text']
                        username = values[2] if len(values) > 2 else ""
                        if username:
                            return f"{realname}[{username}]"
                        return realname
        
        return None
    
    def save_task(self):
        """保存任务"""
        try:
            # 获取选中的目标
            selected_indices = self.targets_listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("警告", "请选择至少一个发送目标")
                return
            
            # 获取选中的目标ID列表
            target_ids = []
            is_private = self.task_target_type.get() == "私聊"
            
            for index in selected_indices:
                target_text = self.targets_listbox.get(index)
                # 从文本中提取ID
                target_id = target_text.split('(')[-1].rstrip(')')
                
                # 如果是私聊，需要添加前缀
                if is_private and not target_id.startswith('S:'):
                    # 添加私聊前缀
                    if hasattr(self.wechat_gui, 'startup') and hasattr(self.wechat_gui.startup, 'user_id'):
                        target_id = f"S:{self.wechat_gui.startup.user_id}_{target_id}"
                
                target_ids.append(target_id)
            
            # 获取消息内容
            message = self.task_message.get('1.0', tk.END).strip()
            if not message:
                messagebox.showwarning("警告", "请输入消息内容")
                return
            
            # 获取日期和时间
            try:
                year = int(self.year_var.get())
                month = int(self.month_var.get())
                day = int(self.day_var.get())
                hour = int(self.task_hour.get())
                minute = int(self.task_minute.get())
                schedule_time = datetime.datetime(year, month, day, hour, minute)
            except Exception as e:
                messagebox.showerror("错误", f"时间格式错误: {e}")
                return
            
            # 获取重复类型
            repeat_type_map = {
                "一次性": "once",
                "每天": "daily",
                "每周": "weekly",
                "每月": "monthly"
            }
            repeat_type = repeat_type_map.get(self.task_repeat_type.get(), "once")
            
            # 获取重复值
            repeat_value = None
            if repeat_type == "weekly":
                repeat_value = int(self.week_day_var.get())
            elif repeat_type == "monthly":
                repeat_value = int(self.month_day_var.get())
            
            # 创建或更新任务
            selected = self.tasks_tree.selection()
            if selected:
                # 更新现有任务
                task_id = selected[0]
                task = self.scheduler.get_task(task_id)
                if task:
                    task.target_id = target_ids
                    task.message = message
                    task.schedule_time = schedule_time
                    task.repeat_type = repeat_type
                    task.repeat_value = repeat_value
                    self.scheduler.update_task(task)
                    messagebox.showinfo("成功", f"任务已更新，将发送到 {len(target_ids)} 个目标")
                else:
                    messagebox.showerror("错误", "找不到选中的任务")
            else:
                # 创建新任务
                task_id = f"task_{int(time.time())}"
                task = SchedulerTask(
                    task_id=task_id,
                    target_id=target_ids,
                    message=message,
                    schedule_time=schedule_time,
                    repeat_type=repeat_type,
                    repeat_value=repeat_value
                )
                self.scheduler.add_task(task)
                messagebox.showinfo("成功", f"任务已添加，将发送到 {len(target_ids)} 个目标")
            
            # 刷新任务列表
            self.refresh_scheduler_tasks()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存任务失败: {e}")
    
    def clear_form(self):
        """清空表单"""
        self.task_target_type.current(0)
        self.on_target_type_change(None)
        
        # 清除选择
        self.targets_listbox.selection_clear(0, tk.END)
        
        self.task_message.delete('1.0', tk.END)
        
        # 重置日期为今天
        today = datetime.datetime.now()
        self.year_var.set(str(today.year))
        self.month_var.set(str(today.month))
        self.day_var.set(str(today.day))
        self.task_hour.set(today.hour)
        self.task_minute.set(today.minute)
        
        # 重置重复类型
        self.task_repeat_type.current(0)
        self.on_repeat_type_change(None)

    def upload_monitor_groups(self):
        """上传监控群txt文件"""
        try:
            # 检查当前是否选择了群聊模式
            if self.task_target_type.get() != "群聊":
                messagebox.showwarning("提示", "请先选择'群聊'模式")
                return

            # 打开文件对话框选择txt文件
            file_path = filedialog.askopenfilename(
                title="选择监控群列表文件",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if not file_path:
                return

            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                group_names = [line.strip() for line in f.readlines() if line.strip()]

            if not group_names:
                messagebox.showwarning("警告", "文件为空或没有有效的群名称")
                return

            # 批量筛选并选择群
            selected_count = 0
            not_found_groups = []

            # 清除当前选择
            self.targets_listbox.selection_clear(0, tk.END)

            # 获取当前列表框中的所有项目
            all_targets = list(self.targets_listbox.get(0, tk.END))

            for group_name in group_names:
                found = False
                for i, target_text in enumerate(all_targets):
                    # 从显示文本中提取群名称（格式：群名称 (群ID)）
                    if target_text.startswith(group_name + " ("):
                        self.targets_listbox.selection_set(i)
                        selected_count += 1
                        found = True
                        break

                if not found:
                    not_found_groups.append(group_name)

            # 显示结果
            result_msg = f"成功选择 {selected_count} 个群"
            if not_found_groups:
                result_msg += f"\n\n未找到以下群：\n" + "\n".join(not_found_groups[:10])
                if len(not_found_groups) > 10:
                    result_msg += f"\n... 还有 {len(not_found_groups) - 10} 个群未找到"

            messagebox.showinfo("上传结果", result_msg)
            self.logger.info(f"批量选择监控群完成，选择了 {selected_count} 个群，{len(not_found_groups)} 个群未找到")

        except Exception as e:
            error_msg = f"上传监控群文件失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)

    def upload_monitor_friends(self):
        """上传监控好友txt文件"""
        try:
            # 检查当前是否选择了私聊模式
            if self.task_target_type.get() != "私聊":
                messagebox.showwarning("提示", "请先选择'私聊'模式")
                return

            # 打开文件对话框选择txt文件
            file_path = filedialog.askopenfilename(
                title="选择监控好友列表文件",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if not file_path:
                return

            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                friend_names = [line.strip() for line in f.readlines() if line.strip()]

            if not friend_names:
                messagebox.showwarning("警告", "文件为空或没有有效的好友名称")
                return

            # 批量筛选并选择好友
            selected_count = 0
            not_found_friends = []

            # 清除当前选择
            self.targets_listbox.selection_clear(0, tk.END)

            # 获取当前列表框中的所有项目
            all_targets = list(self.targets_listbox.get(0, tk.END))

            for friend_name in friend_names:
                found = False
                for i, target_text in enumerate(all_targets):
                    # 从显示文本中提取好友信息（格式：真实姓名[用户名](用户ID) 或 真实姓名(用户ID)）
                    # 检查真实姓名是否匹配
                    if target_text.startswith(friend_name):
                        # 进一步检查是否是完整匹配（避免部分匹配）
                        if ("[" in target_text and target_text.split("[")[0] == friend_name) or \
                           ("(" in target_text and target_text.split("(")[0] == friend_name):
                            self.targets_listbox.selection_set(i)
                            selected_count += 1
                            found = True
                            break
                    # 也检查用户名匹配（在[]中的部分）
                    elif "[" in target_text and "]" in target_text:
                        username_part = target_text.split("[")[1].split("]")[0]
                        if username_part == friend_name:
                            self.targets_listbox.selection_set(i)
                            selected_count += 1
                            found = True
                            break

                if not found:
                    not_found_friends.append(friend_name)

            # 显示结果
            result_msg = f"成功选择 {selected_count} 个好友"
            if not_found_friends:
                result_msg += f"\n\n未找到以下好友：\n" + "\n".join(not_found_friends[:10])
                if len(not_found_friends) > 10:
                    result_msg += f"\n... 还有 {len(not_found_friends) - 10} 个好友未找到"

            messagebox.showinfo("上传结果", result_msg)
            self.logger.info(f"批量选择监控好友完成，选择了 {selected_count} 个好友，{len(not_found_friends)} 个好友未找到")

        except Exception as e:
            error_msg = f"上传监控好友文件失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)