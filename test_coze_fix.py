# -*- coding: utf-8 -*-
"""
测试Coze工作流修复
验证Windows兼容性问题是否解决
"""

import sys
import os

def test_signal_compatibility():
    """测试signal模块兼容性"""
    print("=== 测试signal模块兼容性 ===")
    
    try:
        import signal
        print(f"✅ signal模块导入成功")
        print(f"当前平台: {sys.platform}")
        
        # 检查alarm方法是否存在
        if hasattr(signal, 'alarm'):
            print("✅ signal.alarm 可用 (Unix/Linux系统)")
        else:
            print("❌ signal.alarm 不可用 (Windows系统)")
            print("需要使用替代方案")
            
    except ImportError as e:
        print(f"❌ signal模块导入失败: {e}")

def test_coze_workflow_import():
    """测试Coze工作流模块导入"""
    print("\n=== 测试Coze工作流模块导入 ===")
    
    try:
        # 添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
            
        from coze.coze_api_key import CozeWorkflow
        print("✅ CozeWorkflow类导入成功")
        
        # 尝试创建实例（可能会因为环境变量失败，但不应该有signal错误）
        try:
            workflow = CozeWorkflow()
            print("✅ CozeWorkflow实例创建成功")
        except ValueError as e:
            print(f"⚠️ CozeWorkflow实例创建失败（预期，缺少环境变量）: {e}")
        except AttributeError as e:
            if "signal" in str(e).lower() and "alarm" in str(e).lower():
                print(f"❌ 仍然存在signal.alarm错误: {e}")
                return False
            else:
                print(f"⚠️ 其他AttributeError: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ CozeWorkflow导入失败: {e}")
        return False

def test_workflow_method_signature():
    """测试工作流方法签名"""
    print("\n=== 测试工作流方法签名 ===")
    
    try:
        from coze.coze_api_key import CozeWorkflow
        
        # 检查run_workflow方法的参数
        import inspect
        sig = inspect.signature(CozeWorkflow.run_workflow)
        params = list(sig.parameters.keys())
        
        print(f"run_workflow方法参数: {params}")
        
        # 检查是否移除了timeout参数
        if 'timeout' not in params:
            print("✅ timeout参数已移除，避免signal.alarm问题")
        else:
            print("⚠️ timeout参数仍然存在")
            
        return True
        
    except Exception as e:
        print(f"❌ 方法签名检查失败: {e}")
        return False

def test_alternative_timeout():
    """测试替代超时方案"""
    print("\n=== 测试替代超时方案 ===")
    
    import threading
    import time
    
    def mock_long_operation():
        """模拟长时间操作"""
        time.sleep(2)
        return "操作完成"
    
    def test_thread_timeout():
        """测试线程超时"""
        result = [None]
        exception = [None]
        
        def worker():
            try:
                result[0] = mock_long_operation()
            except Exception as e:
                exception[0] = e
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
        thread.join(timeout=1)  # 1秒超时
        
        if thread.is_alive():
            print("✅ 线程超时机制工作正常")
            return True
        else:
            print("⚠️ 操作在超时前完成")
            return True
    
    try:
        return test_thread_timeout()
    except Exception as e:
        print(f"❌ 线程超时测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Windows兼容性修复测试")
    print("=" * 50)
    
    tests = [
        ("Signal兼容性", test_signal_compatibility),
        ("Coze模块导入", test_coze_workflow_import),
        ("方法签名检查", test_workflow_method_signature),
        ("替代超时方案", test_alternative_timeout),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！Windows兼容性问题已修复")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    main()
