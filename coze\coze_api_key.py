from dotenv import load_dotenv
import os
import sys
import urllib3
from coze.token_manager import TokenManager
import json

# 禁用 SSL 警告
urllib3.disable_warnings()

def get_resource_path(relative_path):
    """获取资源文件的绝对路径，支持PyInstaller打包后的路径"""
    try:
        # PyInstaller 创建临时文件夹，并将路径存储在 _MEIPASS 中
        base_path = sys._MEIPASS
    except Exception:
        # 如果不是打包后的环境，使用当前文件的目录
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

# 加载环境变量 - 支持打包后的路径
env_path = get_resource_path('.env')
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"✅ 已加载环境变量文件: {env_path}")
else:
    # 尝试从当前目录加载
    load_dotenv()
    print("⚠️ 使用默认路径加载环境变量")


class CozeWorkflow:
    """Coze工作流类，用于调用Coze工作流并获取AI回复内容"""

    def __init__(self):
        # 获取配置
        jwt_oauth_client_id = os.getenv("COZE_JWT_OAUTH_CLIENT_ID")
        jwt_oauth_private_key = os.getenv("COZE_JWT_OAUTH_PRIVATE_KEY")
        jwt_oauth_public_key_id = os.getenv("COZE_JWT_OAUTH_PUBLIC_KEY_ID")

        # 处理私钥中的换行符
        if jwt_oauth_private_key:
            jwt_oauth_private_key = jwt_oauth_private_key.replace('\\n', '\n')

        # 检查环境变量
        if not all([jwt_oauth_client_id, jwt_oauth_private_key, jwt_oauth_public_key_id]):
            raise ValueError("缺少必要的环境变量配置")

        # 创建 token 管理器
        self.token_manager = TokenManager(
            client_id=jwt_oauth_client_id,
            private_key=jwt_oauth_private_key,
            public_key_id=jwt_oauth_public_key_id
        )

    def run_workflow(self, workflow_id: str, query: str, max_retries: int = 3) -> str:
        """
        运行Coze工作流并获取AI回复内容

        Args:
            workflow_id (str): 工作流ID
            query (str): 用户查询内容
            max_retries (int): 最大重试次数

        Returns:
            str: AI回复的内容
        """
        import time

        for attempt in range(max_retries):
            try:
                # 获取 Coze 客户端
                coze = self.token_manager.get_coze_client()

                # 运行工作流
                params = {
                    "BOT_USER_INPUT": query
                }

                workflow_result = coze.workflows.runs.create(
                    workflow_id=workflow_id,
                    parameters=params
                )

                # 提取回复内容
                return self._extract_reply_content(workflow_result.data)

            except Exception as e:
                print(f"执行工作流时发生错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # 指数退避

    def _extract_reply_content(self, workflow_data) -> str:
        """
        从工作流返回数据中提取AI回复内容

        Args:
            workflow_data: 工作流返回的数据

        Returns:
            str: 提取的回复内容
        """
        try:
            # 如果workflow_data是字符串，尝试解析为JSON
            if isinstance(workflow_data, str):
                data = json.loads(workflow_data)
            else:
                data = workflow_data

            # 提取data字段的内容
            if isinstance(data, dict) and "data" in data:
                return data["data"]
            else:
                return str(data)

        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"提取回复内容时发生错误: {str(e)}")
            return str(workflow_data)


# 示例使用
if __name__ == "__main__":
    try:
        # 创建工作流实例
        coze_workflow = CozeWorkflow()

        # 运行工作流
        workflow_id = "7529049117402759202"
        query = "你好"

        reply_content = coze_workflow.run_workflow(workflow_id, query)
        print("AI回复内容:")
        print(reply_content)

    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")
        raise