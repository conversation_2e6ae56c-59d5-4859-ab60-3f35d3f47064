# Coze API 修改说明

## 修改概述

根据要求，已将原有的 Coze API 调用方式修改为使用 Coze 工作流（Workflow）的方式。主要修改包括：

## 主要修改内容

### 1. GUI界面修改 (main.py)

#### Coze API 选项卡修改：
- **移除了 API Key 输入框**：不再需要手动输入API Key
- **将 Bot ID 改为 Workflow ID**：`self.bot_id_entry` → `self.workflow_id_entry`
- **添加了测试工作流按钮**：可以测试工作流连接是否正常
- **移除了API Key显示/隐藏功能**：相关的 `toggle_api_key_visibility()` 方法已删除

#### 配置管理修改：
- **配置保存**：将 `api_key` 和 `bot_id` 改为 `workflow_id`
- **配置加载**：相应地加载 `workflow_id` 而不是API相关配置

### 2. 消息处理逻辑修改

#### StartUp类修改：
- **参数检查**：将检查 `api_key` 和 `bot_id` 改为检查 `workflow_id`
- **API调用替换**：
  ```python
  # 原来的调用方式
  response = coze_api.coze_api(
      self.api_key, 
      self.bot_id, 
      content,
      from_wxid,
      conversation_id
  )
  
  # 新的调用方式
  from coze.coze_api_key import CozeWorkflow
  coze_workflow = CozeWorkflow()
  response = coze_workflow.run_workflow(
      self.workflow_id, 
      content
  )
  ```

#### 监控启动/停止逻辑修改：
- **启动监控**：设置 `workflow_id` 而不是 `api_key` 和 `bot_id`
- **停止监控**：清除 `workflow_id` 参数

### 3. 测试功能修改

#### 新的测试方法：
- **`test_coze_workflow()`**：替换原来的 `test_coze_api()` 方法
- 使用 `CozeWorkflow` 类进行测试连接
- 测试消息为 "测试连接"

## 技术实现细节

### 工作流调用方式
使用 `coze\coze_api_key.py` 中的 `CozeWorkflow` 类：
- 自动处理JWT OAuth认证
- 通过环境变量获取认证信息
- 调用 `run_workflow(workflow_id, query)` 方法

### 环境变量要求
需要在 `.env` 文件中配置：
- `COZE_JWT_OAUTH_CLIENT_ID`
- `COZE_JWT_OAUTH_PRIVATE_KEY`
- `COZE_JWT_OAUTH_PUBLIC_KEY_ID`

## 使用说明

### 1. 安装依赖
首先安装Coze工作流所需的依赖包：
```bash
pip install -r requirements_coze.txt
```

或者手动安装：
```bash
pip install python-dotenv cozepy urllib3
```

### 2. 配置环境变量
在项目根目录创建 `.env` 文件，添加以下配置：
```
COZE_JWT_OAUTH_CLIENT_ID=your_client_id
COZE_JWT_OAUTH_PRIVATE_KEY=your_private_key
COZE_JWT_OAUTH_PUBLIC_KEY_ID=your_public_key_id
```

### 3. 配置工作流ID
在 "Coze API" 选项卡中输入有效的工作流ID

### 4. 测试连接
点击 "测试工作流" 按钮验证配置是否正确

### 5. 启动监控
配置完成后，点击 "启动监控" 开始自动回复

## 优势

1. **更安全**：不需要在界面中输入和存储API Key
2. **更灵活**：工作流可以包含更复杂的逻辑处理
3. **更易维护**：认证信息通过环境变量管理
4. **更稳定**：使用官方SDK进行调用

## 注意事项

1. 确保 `.env` 文件中的认证信息正确
2. 工作流ID必须是有效的Coze工作流ID
3. 工作流需要配置正确的输入参数 `BOT_USER_INPUT`
4. 确保网络连接正常，能够访问Coze API服务

## 测试文件

创建了 `test_workflow.py` 文件用于独立测试工作流功能，可以在集成到主程序前先验证工作流是否正常工作。
