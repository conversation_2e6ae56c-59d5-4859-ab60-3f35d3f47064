from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, AsyncGenerator
import ntwork
import logging
import json
import time
from datetime import datetime
from contextlib import asynccontextmanager
from collections import defaultdict
import asyncio

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger()

# 全局变量存储登录信息
login_info = {
    "user_id": None,
    "name": None,
    "is_logged_in": False
}

# 初始化微信实例
wechat = None

# 存储消息的字典
message_store = defaultdict(list)

# 用于追踪最新消息ID
last_message_id = 0

def on_recv_message(wechat_instance: ntwork.WeWork, message):
    """接收消息的回调函数"""
    try:
        global last_message_id
        data = message["data"]
        content = data.get("content", "").strip()
        if not content:
            return
            
        room_wxid = data.get("conversation_id")
        from_wxid = data.get("sender")
        sender_name = data.get("sender_name", "")
        
        # 创建消息记录
        msg_record = {
            "id": last_message_id + 1,  # 递增的消息ID
            "timestamp": datetime.now().isoformat(),
            "content": content,
            "sender_id": from_wxid,
            "sender_name": sender_name,
            "conversation_id": room_wxid
        }
        
        last_message_id += 1  # 更新最新消息ID
        
        # 存储消息
        message_store[room_wxid].append(msg_record)
        
        # 保持每个会话最多存储100条消息
        if len(message_store[room_wxid]) > 100:
            message_store[room_wxid] = message_store[room_wxid][-100:]
            
        logger.info(f"收到消息: {json.dumps(msg_record, ensure_ascii=False)}")
    except Exception as e:
        logger.error(f"处理消息失败: {str(e)}")

def init_wechat():
    """初始化微信"""
    global wechat
    try:
        # 确保之前的实例被清理
        if wechat:
            try:
                wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
            except:
                pass
            ntwork.exit_()
            time.sleep(1)
        
        # 初始化新实例
        wechat = ntwork.WeWork()
        wechat.open(smart=True)
        logger.info("等待登录......")
        wechat.wait_login(timeout=500)
        logger.info("登录成功，等待数据同步...")
        time.sleep(20)  # 等待数据同步
        
        # 注册消息回调
        wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(on_recv_message)
        logger.info("消息回调注册成功")
        
        # 获取登录信息
        max_retries = 3
        for i in range(max_retries):
            try:
                info = wechat.get_login_info()
                if info and 'user_id' in info:
                    login_info["user_id"] = info["user_id"]
                    login_info["name"] = info.get("nickname") or info.get("username")
                    login_info["is_logged_in"] = True
                    logger.info(f"登录信息: user_id:{login_info['user_id']}, name:{login_info['name']}")
                    return True
                else:
                    if i < max_retries - 1:
                        logger.warning(f"获取登录信息不完整，正在重试... ({i + 1}/{max_retries})")
                        time.sleep(2)
                    else:
                        raise RuntimeError("无法获取完整的登录信息")
            except Exception as e:
                if i < max_retries - 1:
                    logger.warning(f"获取登录信息失败，正在重试... ({i + 1}/{max_retries})")
                    time.sleep(2)
                else:
                    raise
    except Exception as e:
        logger.error(f"初始化微信失败: {str(e)}")
        logger.error("错误详情: ", exc_info=True)
        return False

@asynccontextmanager
async def lifespan(app: FastAPI):
    """生命周期管理"""
    # 启动时初始化微信
    if not init_wechat():
        raise Exception("微信初始化失败")
    yield
    # 关闭时清理微信实例
    if wechat:
        try:
            wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
            ntwork.exit_()
        except:
            pass

# 数据模型
class MessageRequest(BaseModel):
    conversation_id: str
    content: str

class RoomMessageRequest(BaseModel):
    room_id: str
    content: str

class Response(BaseModel):
    code: int = 200
    message: str = "success"
    data: Optional[dict] = None

app = FastAPI(title="微信API服务", lifespan=lifespan)

@app.get("/api/rooms", response_model=Response)
async def get_rooms():
    """获取所有群列表"""
    try:
        if not login_info["is_logged_in"] or not wechat:
            raise HTTPException(status_code=401, detail="微信未登录")
            
        all_rooms = []
        current_page = 1
        page_size = 500
        
        while True:
            rooms = wechat.get_rooms(page_num=current_page, page_size=page_size)
            if not rooms or 'room_list' not in rooms or not rooms['room_list']:
                break
                
            all_rooms.extend(rooms['room_list'])
            
            if len(rooms['room_list']) < page_size:
                break
                
            current_page += 1
            
        return Response(
            data={
                "total": len(all_rooms),
                "rooms": all_rooms
            }
        )
    except Exception as e:
        logger.error(f"获取群列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# @app.get("/api/room/{room_id}/messages", response_model=Response)
# async def get_room_messages(room_id: str):
#     """获取指定群的消息"""
#     try:
#         if not login_info["is_logged_in"] or not wechat:
#             raise HTTPException(status_code=401, detail="微信未登录")
            
#         # 从消息存储中获取消息
#         messages = message_store.get(room_id, [])
#         return Response(data={"messages": messages})
#     except Exception as e:
#         logger.error(f"获取群消息失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/message", response_model=Response)
async def send_message(request: MessageRequest):
    """发送消息（支持群聊和私聊）
    
    Args:
        request: 包含conversation_id和content的请求对象
        conversation_id: 目标会话的ID,群聊 R:xxxx ；私聊 S:{self.user_id}_{from_wxid}
        content: 要发送的消息内容
    
    Returns:
    """
    try:
        if not login_info["is_logged_in"] or not wechat:
            raise HTTPException(status_code=401, detail="微信未登录")
            
        # 发送消息
        wechat.send_text(request.conversation_id, request.content)
        
        return Response(message="消息发送成功")
    except Exception as e:
        logger.error(f"发送消息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/friends", response_model=Response)
async def get_friends():
    """获取所有好友列表"""
    try:
        if not login_info["is_logged_in"] or not wechat:
            raise HTTPException(status_code=401, detail="微信未登录")
            
        all_friends = []
        current_page = 1
        page_size = 50
        
        # 获取内部联系人
        while True:
            inner_contacts = wechat.get_inner_contacts(page_num=current_page, page_size=page_size)
            if not inner_contacts or 'user_list' not in inner_contacts or not inner_contacts['user_list']:
                break
                
            all_friends.extend(inner_contacts['user_list'])
            
            if len(inner_contacts['user_list']) < page_size:
                break
                
            current_page += 1
            
        # 重置页码获取外部联系人
        current_page = 1
        
        # 获取外部联系人
        while True:
            external_contacts = wechat.get_external_contacts(page_num=current_page, page_size=page_size)
            if not external_contacts or 'user_list' not in external_contacts or not external_contacts['user_list']:
                break
                
            all_friends.extend(external_contacts['user_list'])
            
            if len(external_contacts['user_list']) < page_size:
                break
                
            current_page += 1
            
        return Response(
            data={
                "total": len(all_friends),
                "friends": all_friends
            }
        )
    except Exception as e:
        logger.error(f"获取好友列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/messages/realtime", response_model=Response)
async def get_realtime_messages(limit: int = 10):
    """获取实时文本消息
    
    Args:
        limit: 返回最近的消息数量，默认10条
    """
    try:
        if not login_info["is_logged_in"] or not wechat:
            raise HTTPException(status_code=401, detail="微信未登录")
            
        # 获取所有会话的最新消息
        all_messages = []
        for conversation_id, messages in message_store.items():
            if messages:  # 只处理有消息的会话
                latest_message = messages[-1]  # 获取最新的一条消息
                all_messages.append(latest_message)
        
        # 按时间戳排序
        all_messages.sort(key=lambda x: x["timestamp"], reverse=True)
        
        # 返回指定数量的消息
        return Response(
            data={
                "total": len(all_messages),
                "messages": all_messages[:limit]
            }
        )
    except Exception as e:
        logger.error(f"获取实时消息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/messages/poll")
async def poll_messages(last_id: int = 0) -> StreamingResponse:
    """轮询获取新消息
    
    Args:
        last_id: 上次获取的最后一条消息ID，用于增量获取
    """
    async def message_generator() -> AsyncGenerator[str, None]:
        try:
            if not login_info["is_logged_in"] or not wechat:
                yield json.dumps({"code": 401, "message": "微信未登录"}) + "\n"
                return
            
            # 永久轮询等待新消息
            while True:
                # 获取所有新消息
                for conversation_id, messages in message_store.items():
                    for msg in messages:
                        if msg["id"] > last_id:
                            # 找到一条新消息就立即返回
                            response = {
                                "code": 200,
                                "message": "success",
                                "data": {
                                    "last_id": msg["id"],
                                    "message": msg
                                }
                            }
                            yield json.dumps(response, ensure_ascii=False) + "\n"
                            return
                
                # 等待1秒后继续检查
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error(f"轮询消息失败: {str(e)}")
            yield json.dumps({"code": 500, "message": str(e)}) + "\n"
    
    return StreamingResponse(
        message_generator(),
        media_type="application/x-ndjson"
    )

if __name__ == "__main__":
    import sys
    import uvicorn

    if getattr(sys, 'frozen', False):
    # 如果是打包后的可执行文件，直接加载 app
        uvicorn.run(app, host="0.0.0.0", port=8002)
    else:
    # 如果是源代码运行，使用传统的模块加载方式
        uvicorn.run("api:app", host="0.0.0.0", port=8002)
