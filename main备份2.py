# -*- coding: utf-8 -*-
import sys, time, re, os
import logging, ntwork
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from datetime import datetime
import json
import keyboard
import subprocess
import requests
import ttkbootstrap as ttk
from ttkbootstrap import Style
from ttkbootstrap.constants import *  # 导入常量
from scheduler import Scheduler
from scheduler_tab import SchedulerTab

# 在文件顶部添加一个全局变量来跟踪实例
_instance = None

# 自定义日志处理器，将日志输出到GUI
class GuiHandler(logging.Handler):
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)

        def append():
            self.text_widget.configure(state="normal")
            self.text_widget.insert(tk.END, msg + "\n")
            self.text_widget.see(tk.END)
            self.text_widget.configure(state="disabled")

        self.text_widget.after(0, append)


class WeChatGUI:
    def __init__(self):
        global _instance
        if _instance is not None:
            raise RuntimeError("WeChatGUI 已经在运行")
        _instance = self
        
        # 使用 ttkbootstrap 的样式
        self.style = Style(theme='cyborg')  # 使用深色科技风主题
        self.root = self.style.master
        self.root.title("微信Coze机器人")
        self.root.geometry("1350x700")
        
        # 设置窗口在屏幕中央
        self.center_window()
        
        # 设置窗口图标（如果有的话）
        # self.root.iconbitmap('path/to/icon.ico')
        
        self.startup = None
        
        # 初始化默认提示语
        self.replys = [
            "亲爱的家长，您好！😊 关于软件的问题，随时欢迎您咨询哦！如果有其他疑问，可以直接联系小易老师，我们随时为您服务！📚✨",
            "您好！😊 如果有其他关于【E听说中学】软件使用的问题，随时联系我哦！也可以@小易老师，我们会尽快为您解答！📚✨",
            "家长您好！😊 非常抱歉，这个问题我无法为您解答。如果有其他关于【E听说中学】软件使用的问题，随时可以联系我哦！您也可以@小易老师。期待为您提供帮助！📚✨"
        ]
        
        # 初始化暂停群列表
        self.paused_rooms = []
        
        # 创建主框架
        self.create_widgets()
        self.setup_logging()
        
        # 初始化调度器
        self.scheduler = Scheduler(logger=self.logger)
        
        # 现在可以设置定时任务标签页了
        self.setup_scheduler_tab()
        
        self.load_config()
        self.init_wechat()

    def __del__(self):
        global _instance
        _instance = None

    def center_window(self):
        """将窗口居中显示"""
        # 获取屏幕的宽度和高度
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 修改窗口尺寸
        window_width = 1350
        window_height = 700
        
        # 计算窗口居中的坐标
        center_x = int((screen_width - window_width) / 2)
        center_y = int((screen_height - window_height) / 2)
        
        # 设置窗口位置
        self.root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")

    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
        )
        self.logger = logging.getLogger()

        # 添加GUI处理器
        gui_handler = GuiHandler(self.log_text)
        gui_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        )
        self.logger.addHandler(gui_handler)

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建群管理选项卡
        self.room_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.room_tab, text="群管理")
        
        # 创建Coze API选项卡
        self.coze_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.coze_tab, text="Coze API")
        
        # 创建日志选项卡
        self.log_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.log_tab, text="运行日志")
        
        # 创建好友管理选项卡
        self.friend_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.friend_tab, text="好友管理")
        
        # 添加定时任务标签页
        self.scheduler_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.scheduler_tab, text="定时任务")
        
        # 设置群管理选项卡内容
        self.setup_room_tab()
        
        # 设置Coze API选项卡内容
        self.setup_coze_tab()
        
        # 设置日志选项卡内容
        self.setup_log_tab()
        
        # 设置好友管理选项卡内容
        self.setup_friend_tab()
        
        # 注意：定时任务标签页的设置将在scheduler初始化后进行
        # 不要在这里调用setup_scheduler_tab()
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame, padding="5")
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动监控", command=self.start_monitoring, bootstyle="success")
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring, state="disabled", bootstyle="danger")
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 添加保存配置按钮
        save_btn = ttk.Button(control_frame, text="保存配置", command=self.save_config, bootstyle="info-outline")
        save_btn.pack(side=tk.LEFT, padx=5)

    def setup_room_tab(self):
        # 群列表管理区域
        room_frame = ttk.Frame(self.room_tab, padding="10")
        room_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧群列表
        left_frame = ttk.LabelFrame(room_frame, text="所有群列表", padding="10", bootstyle="primary")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 刷新按钮
        refresh_btn = ttk.Button(
            left_frame, 
            text="刷新群列表", 
            command=self.refresh_room_list,
            bootstyle="primary-outline"
        )
        refresh_btn.pack(side=tk.TOP, pady=(0, 5), anchor=tk.E)
        
        # 创建滚动条和树形视图的框架
        tree_frame = ttk.Frame(left_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.all_rooms_tree = ttk.Treeview(
            tree_frame, 
            columns=('room_id',),
            height=10,
            bootstyle="primary"
        )
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.all_rooms_tree.yview, bootstyle="primary-round")
        self.all_rooms_tree.configure(yscrollcommand=scrollbar.set)
        
        self.all_rooms_tree['show'] = 'tree headings'
        self.all_rooms_tree.heading('#0', text='群名称')
        self.all_rooms_tree.heading('room_id', text='群ID')
        self.all_rooms_tree.column('#0', width=200, stretch=True)
        self.all_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.all_rooms_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 中间操作按钮
        btn_frame = ttk.Frame(room_frame)
        btn_frame.pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Button(btn_frame, text="添加 >>", command=self.add_selected_room, bootstyle="primary-outline").pack(pady=5)
        ttk.Button(btn_frame, text="<< 移除", command=self.remove_selected_room, bootstyle="danger-outline").pack(pady=5)

        # 右侧监控群列表
        right_frame = ttk.LabelFrame(room_frame, text="监控群列表", padding="10", bootstyle="primary")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 监控群树形视图
        monitor_frame = ttk.Frame(right_frame)
        monitor_frame.pack(fill=tk.BOTH, expand=True)
        
        self.monitored_rooms_tree = ttk.Treeview(
            monitor_frame,
            columns=('room_id',),
            height=10,
            bootstyle="primary"
        )
        monitor_scrollbar = ttk.Scrollbar(monitor_frame, orient="vertical", command=self.monitored_rooms_tree.yview, bootstyle="primary-round")
        self.monitored_rooms_tree.configure(yscrollcommand=monitor_scrollbar.set)
        
        self.monitored_rooms_tree['show'] = 'tree headings'
        self.monitored_rooms_tree.heading('#0', text='群名称')
        self.monitored_rooms_tree.heading('room_id', text='群ID')
        self.monitored_rooms_tree.column('#0', width=200, stretch=True)
        self.monitored_rooms_tree.column('room_id', width=150, stretch=True)
        
        self.monitored_rooms_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monitor_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_coze_tab(self):
        # Coze API配置区域
        coze_frame = ttk.Frame(self.coze_tab, padding="10")
        coze_frame.pack(fill=tk.BOTH, expand=True)
        
        # API配置
        config_frame = ttk.LabelFrame(coze_frame, text="Coze API配置", padding="10", bootstyle="info")
        config_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)
        
        # API Key
        ttk.Label(config_frame, text="API Key:", bootstyle="inverse-info").grid(row=0, column=0, sticky=tk.W, pady=10)
        self.api_key_entry = ttk.Entry(config_frame, width=50, show="*")
        self.api_key_entry.grid(row=0, column=1, padx=5, pady=10, sticky=tk.W)
        
        # 显示/隐藏API Key按钮
        self.show_api_key = tk.BooleanVar(value=False)
        ttk.Checkbutton(
            config_frame, 
            text="显示", 
            variable=self.show_api_key, 
            command=self.toggle_api_key_visibility,
            bootstyle="info-round-toggle"
        ).grid(row=0, column=2, padx=5)
        
        # Bot ID
        ttk.Label(config_frame, text="Bot ID:", bootstyle="inverse-info").grid(row=1, column=0, sticky=tk.W, pady=10)
        self.bot_id_entry = ttk.Entry(config_frame, width=50)
        self.bot_id_entry.grid(row=1, column=1, padx=5, pady=10, sticky=tk.W)
        
        # 高级设置
        advanced_frame = ttk.LabelFrame(coze_frame, text="高级设置", padding="10", bootstyle="secondary")
        advanced_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)
        
        # 是否需要@机器人
        ttk.Label(advanced_frame, text="需要@机器人才回复:", bootstyle="inverse-secondary").grid(row=0, column=0, sticky=tk.W, pady=10)
        self.require_at_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            advanced_frame, 
            variable=self.require_at_var,
            bootstyle="secondary-round-toggle"
        ).grid(row=0, column=1, padx=5, pady=10, sticky=tk.W)
        
        # 无法回答时的回复设置
        reply_frame = ttk.LabelFrame(coze_frame, text="无法回答时的提示语", padding="10", bootstyle="warning")
        reply_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        ttk.Label(reply_frame, text="提示语列表（每行一条）:", bootstyle="inverse-warning").pack(anchor=tk.W, pady=(0, 5))
        
        # 创建带滚动条的文本框
        reply_text_frame = ttk.Frame(reply_frame)
        reply_text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.reply_text = tk.Text(reply_text_frame, height=10, width=60, wrap=tk.WORD)
        reply_scrollbar = ttk.Scrollbar(reply_text_frame, command=self.reply_text.yview)
        self.reply_text.configure(yscrollcommand=reply_scrollbar.set)
        
        self.reply_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        reply_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加保存按钮
        ttk.Button(
            reply_frame, 
            text="保存提示语", 
            command=self.save_reply_texts,
            bootstyle="warning-outline"
        ).pack(anchor=tk.E, pady=(5, 0))

    def save_reply_texts(self):
        """保存无法回答时的提示语"""
        reply_text = self.reply_text.get('1.0', tk.END).strip()
        self.replys = [line.strip() for line in reply_text.split('\n') if line.strip()]
        self.logger.info(f"已保存 {len(self.replys)} 条提示语")
        
        # 立即保存到配置文件
        self.save_config()
        
        messagebox.showinfo("成功", f"已保存 {len(self.replys)} 条提示语并写入配置文件")
        
    def toggle_api_key_visibility(self):
        if self.show_api_key.get():
            self.api_key_entry.config(show="")
        else:
            self.api_key_entry.config(show="*")

    def test_coze_api(self):
        api_key = self.api_key_entry.get().strip()
        bot_id = self.bot_id_entry.get().strip()
        
        if not api_key or not bot_id:
            messagebox.showerror("错误", "API Key和Bot ID不能为空")
            return
        
        try:
            # 导入coze_api模块
            import coze_api
            
            # 测试API连接
            test_message = "测试连接"
            test_user = "test_user"
            test_conversation = "test_conversation"
            
            response = coze_api.coze_api(api_key, bot_id, test_message, test_user, test_conversation)
            
            if response:
                messagebox.showinfo("成功", f"API连接成功！\n响应: {response}")
            else:
                messagebox.showerror("错误", "API连接失败，未收到有效响应")
        except Exception as e:
            messagebox.showerror("错误", f"API连接测试失败: {str(e)}")

    def setup_log_tab(self):
        # 日志显示区
        log_frame = ttk.Frame(self.log_tab, padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = ttk.Text(log_frame, wrap=tk.WORD, height=24)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.configure(state="disabled")
        
        # 添加滚动条
        log_scrollbar = ttk.Scrollbar(self.log_text, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_friend_tab(self):
        # 好友列表管理区域
        friend_frame = ttk.Frame(self.friend_tab, padding="10")
        friend_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧好友列表
        left_frame = ttk.LabelFrame(friend_frame, text="所有好友列表", padding="10", bootstyle="primary")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 刷新按钮
        refresh_btn = ttk.Button(
            left_frame, 
            text="刷新好友列表", 
            command=self.refresh_friend_list,
            bootstyle="primary-outline"
        )
        refresh_btn.pack(side=tk.TOP, pady=(0, 5), anchor=tk.E)
        
        # 创建滚动条和树形视图的框架
        tree_frame = ttk.Frame(left_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.all_friends_tree = ttk.Treeview(
            tree_frame, 
            columns=('user_id', 'nickname', 'username'),
            height=10,
            bootstyle="primary"
        )
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.all_friends_tree.yview, bootstyle="primary-round")
        self.all_friends_tree.configure(yscrollcommand=scrollbar.set)
        
        self.all_friends_tree['show'] = 'tree headings'
        self.all_friends_tree.heading('#0', text='真实姓名')
        self.all_friends_tree.heading('user_id', text='用户ID')
        self.all_friends_tree.heading('nickname', text='昵称')
        self.all_friends_tree.heading('username', text='用户名')
        self.all_friends_tree.column('#0', width=120, stretch=True)
        self.all_friends_tree.column('user_id', width=120, stretch=True)
        self.all_friends_tree.column('nickname', width=120, stretch=True)
        self.all_friends_tree.column('username', width=120, stretch=True)
        
        self.all_friends_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 中间操作按钮
        btn_frame = ttk.Frame(friend_frame)
        btn_frame.pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Button(btn_frame, text="添加管理员 >>", command=self.add_selected_friend, bootstyle="primary-outline").pack(pady=5)
        ttk.Button(btn_frame, text="<< 移除", command=self.remove_selected_friend, bootstyle="danger-outline").pack(pady=5)
        ttk.Button(btn_frame, text="添加通知 >>", command=self.add_notify_friend, bootstyle="success-outline").pack(pady=5)
        ttk.Button(btn_frame, text="<< 移除", command=self.remove_notify_friend, bootstyle="danger-outline").pack(pady=5)

        # 右侧分为上下两部分
        right_frame = ttk.Frame(friend_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 管理员列表
        admin_frame = ttk.LabelFrame(right_frame, text="管理员列表", padding="10", bootstyle="primary")
        admin_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        admin_tree_frame = ttk.Frame(admin_frame)
        admin_tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.admin_friends_tree = ttk.Treeview(
            admin_tree_frame,
            columns=('user_id', 'nickname', 'username'),
            height=5,
            bootstyle="primary"
        )
        admin_scrollbar = ttk.Scrollbar(admin_tree_frame, orient="vertical", command=self.admin_friends_tree.yview, bootstyle="primary-round")
        self.admin_friends_tree.configure(yscrollcommand=admin_scrollbar.set)
        
        self.admin_friends_tree['show'] = 'tree headings'
        self.admin_friends_tree.heading('#0', text='真实姓名')
        self.admin_friends_tree.heading('user_id', text='用户ID')
        self.admin_friends_tree.heading('nickname', text='昵称')
        self.admin_friends_tree.heading('username', text='用户名')
        self.admin_friends_tree.column('#0', width=120, stretch=True)
        self.admin_friends_tree.column('user_id', width=120, stretch=True)
        self.admin_friends_tree.column('nickname', width=120, stretch=True)
        self.admin_friends_tree.column('username', width=120, stretch=True)
        
        self.admin_friends_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        admin_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 通知列表
        notify_frame = ttk.LabelFrame(right_frame, text="接收通知的好友", padding="10", bootstyle="success")
        notify_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        notify_tree_frame = ttk.Frame(notify_frame)
        notify_tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.notify_friends_tree = ttk.Treeview(
            notify_tree_frame,
            columns=('user_id', 'nickname', 'username'),
            height=5,
            bootstyle="success"
        )
        notify_scrollbar = ttk.Scrollbar(notify_tree_frame, orient="vertical", command=self.notify_friends_tree.yview, bootstyle="success-round")
        self.notify_friends_tree.configure(yscrollcommand=notify_scrollbar.set)
        
        self.notify_friends_tree['show'] = 'tree headings'
        self.notify_friends_tree.heading('#0', text='真实姓名')
        self.notify_friends_tree.heading('user_id', text='用户ID')
        self.notify_friends_tree.heading('nickname', text='昵称')
        self.notify_friends_tree.heading('username', text='用户名')
        self.notify_friends_tree.column('#0', width=120, stretch=True)
        self.notify_friends_tree.column('user_id', width=120, stretch=True)
        self.notify_friends_tree.column('nickname', width=120, stretch=True)
        self.notify_friends_tree.column('username', width=120, stretch=True)
        
        self.notify_friends_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        notify_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def init_wechat(self):
        def run():
            try:
                # 确保之前的实例被清理
                if self.startup:
                    try:
                        # 注册空回调替代注销
                        self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
                    except:
                        pass
                    self.startup = None
                    time.sleep(1)  # 等待资源释放
                
                # 初始化新实例
                self.startup = StartUp(self)
                
                try:
                    self.startup.wechat.open(smart=True)
                except Exception as e:
                    self.logger.error(f"打开企业微信失败: {e}")
                    # 尝试重新初始化 ntwork
                    ntwork.exit_()
                    time.sleep(2)
                    self.startup.wechat = ntwork.WeWork()
                    self.startup.wechat.open(smart=True)
                
                self.logger.info("等待登录......")
                self.startup.wechat.wait_login(timeout=500)
                self.logger.info("登录成功，等待数据同步...")
                time.sleep(20)  # 增加等待时间到20秒，让数据更充分同步
                
                # 添加重试机制获取登录信息
                max_retries = 3
                for i in range(max_retries):
                    try:
                        login_info = self.startup.wechat.get_login_info()
                        if login_info and 'user_id' in login_info:
                            self.startup.user_id = login_info["user_id"]
                            if login_info.get("nickname") == '':
                                self.startup.name = login_info.get("username")
                            else:
                                self.startup.name = login_info.get("nickname")
                            self.logger.info(f"登录信息: user_id:{self.startup.user_id}, name:{self.startup.name}")
                            break
                        else:
                            if i < max_retries - 1:
                                self.logger.warning(f"获取登录信息不完整，正在重试... ({i + 1}/{max_retries})")
                                time.sleep(2)  # 等待2秒后重试
                            else:
                                raise RuntimeError("无法获取完整的登录信息")
                    except Exception as e:
                        if i < max_retries - 1:
                            self.logger.warning(f"获取登录信息失败，正在重试... ({i + 1}/{max_retries})")
                            time.sleep(2)
                        else:
                            raise

                # 参考订单查询\main3.0 copy 3.py的方式获取群列表
                self.logger.info("正在获取群列表...")
                
                # 尝试使用不同的方法获取群列表
                rooms = None
                success = False
                
                # 方法1: 直接获取群列表
                for i in range(max_retries):
                    try:
                        rooms = self.startup.wechat.get_rooms()
                        if rooms and 'room_list' in rooms and rooms['room_list']:
                            self.logger.info(f"方法1成功获取群列表，共 {len(rooms['room_list'])} 个群")
                            success = True
                            break
                        else:
                            self.logger.warning(f"方法1获取群列表为空，尝试重试... ({i + 1}/{max_retries})")
                            time.sleep(3)
                    except Exception as e:
                        self.logger.warning(f"方法1获取群列表失败: {e}，尝试重试... ({i + 1}/{max_retries})")
                        time.sleep(3)
                
                # 方法2: 如果方法1失败，尝试使用搜索方式获取群
                if not success:
                    self.logger.info("尝试方法2获取群列表...")
                    try:
                        # 搜索所有群
                        search_result = self.startup.wechat.search_contacts("群")
                        if search_result and isinstance(search_result, list) and len(search_result) > 0:
                            # 构造与get_rooms相同格式的结果
                            room_list = []
                            for room in search_result:
                                if room.get('type') == 2:  # 群聊类型
                                    room_list.append({
                                        'nickname': room.get('nickname', '未知群名'),
                                        'conversation_id': room.get('conversation_id', '')
                                    })
                            
                            if room_list:
                                rooms = {'room_list': room_list}
                                self.logger.info(f"方法2成功获取群列表，共 {len(room_list)} 个群")
                                success = True
                    except Exception as e:
                        self.logger.warning(f"方法2获取群列表失败: {e}")
                
                # 方法3: 如果前两种方法都失败，尝试获取会话列表
                if not success:
                    self.logger.info("尝试方法3获取群列表...")
                    try:
                        # 获取最近的会话列表
                        conversations = self.startup.wechat.get_conversations()
                        if conversations and isinstance(conversations, list) and len(conversations) > 0:
                            # 过滤出群聊会话
                            room_list = []
                            for conv in conversations:
                                if conv.get('type') == 2:  # 群聊类型
                                    room_list.append({
                                        'nickname': conv.get('nickname', '未知群名'),
                                        'conversation_id': conv.get('conversation_id', '')
                                    })
                            
                            if room_list:
                                rooms = {'room_list': room_list}
                                self.logger.info(f"方法3成功获取群列表，共 {len(room_list)} 个群")
                                success = True
                    except Exception as e:
                        self.logger.warning(f"方法3获取群列表失败: {e}")
                
                # 如果获取到群列表，则加载到界面
                if success and rooms and 'room_list' in rooms and rooms['room_list']:
                    # 清空现有列表
                    for item in self.all_rooms_tree.get_children():
                        self.all_rooms_tree.delete(item)
                    
                    # 添加新的群列表
                    room_list = rooms.get('room_list', [])
                    for room in room_list:
                        try:
                            nickname = room.get('nickname', '未知群名')
                            conversation_id = room.get('conversation_id', '')
                            if not conversation_id:
                                continue
                            
                            self.all_rooms_tree.insert('', 'end', 
                                text=nickname,
                                values=(conversation_id,),
                                tags=('room',))
                            self.logger.info(f"加载群: {nickname} ({conversation_id})")
                        except Exception as e:
                            self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                            continue
                    
                    self.logger.info(f"群列表加载完成，共 {len(room_list)} 个群")
                else:
                    self.logger.error("所有方法都无法获取群列表，请检查企业微信状态")
                
                # 获取好友列表
                self.refresh_friend_list()
                
            except Exception as e:
                self.logger.error(f"初始化微信失败: {str(e)}")
                self.logger.error(f"错误详情: ", exc_info=True)
                if self.startup:
                    self.startup.exit_program()
                    self.startup = None
                # 重置按钮状态
                self.root.after(0, self._reset_button_state)

        # 在新线程中初始化微信
        threading.Thread(target=run, daemon=True).start()

    def start_monitoring(self):
        global _instance
        if _instance is not self:
            self.logger.error("检测到多个程序实例，请关闭其他窗口")
            return
            
        # 检查是否已登录
        if not self.startup:
            self.logger.error("微信未初始化，请重启程序")
            return
            
        try:
            login_info = self.startup.wechat.get_login_info()
            if not login_info:
                self.logger.error("微信未登录，请先登录")
                return
        except:
            self.logger.error("获取登录状态失败")
            return
            
        # 检查Coze API配置
        api_key = self.api_key_entry.get().strip()
        bot_id = self.bot_id_entry.get().strip()
        
        if not api_key or not bot_id:
            self.logger.error("API Key和Bot ID不能为空，请在Coze API选项卡中配置")
            return
            
        self.save_config()  # 启动前保存当前配置
        
        # 设置Coze API参数
        try:
            # 直接设置API参数
            self.startup.api_key = api_key
            self.startup.bot_id = bot_id
            self.startup.require_at = self.require_at_var.get()
            
            # 更新提示语列表
            self.startup.replys = self.replys
            
            # 更新暂停群列表
            self.startup.paused_rooms = self.paused_rooms
            
            # 注册消息回调
            self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(self.startup.on_recv_message)
            self.logger.info("消息回调注册成功")
            
            # 设置调度器的微信实例
            self.scheduler.set_wechat(self.startup.wechat)
            
            # 启动调度器
            self.scheduler.start()
            
            # 更新按钮状态
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.logger.info("监控已启动")
            
        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            self.logger.error("错误详情: ", exc_info=True)

    def stop_monitoring(self):
        if self.startup:
            # 取消消息回调
            self.startup.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(lambda x, y: None)
            # 设置退出标志，确保消息处理循环会检查这个标志
            self.startup.exit_flag = True
            # 清除API参数，确保即使有消息进来也不会调用API
            if hasattr(self.startup, 'api_key'):
                delattr(self.startup, 'api_key')
            if hasattr(self.startup, 'bot_id'):
                delattr(self.startup, 'bot_id')
            
            self.logger.info("消息回调已取消，API参数已清除")
            
             # 在新线程中停止调度器
            threading.Thread(target=self._stop_scheduler, daemon=True).start()
            
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.logger.info("监控已停止")
    def _stop_scheduler(self):
        self.scheduler.stop()  # 确保这里的stop方法是优化过的

    def run(self):
        try:
            self.root.mainloop()
        finally:
            global _instance
            _instance = None
            if self.startup:
                self.startup.exit_program()

    def load_config(self):
        try:
            if os.path.exists("config.json"):
                with open("config.json", "r", encoding="utf-8") as f:
                    config = json.load(f)
                    
                    # 加载Coze API配置
                    self.api_key_entry.delete(0, tk.END)
                    self.api_key_entry.insert(0, config.get('api_key', ''))
                    
                    self.bot_id_entry.delete(0, tk.END)
                    self.bot_id_entry.insert(0, config.get('bot_id', ''))
                    
                    # 加载高级设置
                    self.require_at_var.set(config.get('require_at', True))
                    
                    # 加载无法回答时的提示语
                    self.replys = config.get('replys', [
                        "亲爱的家长，您好！😊 关于软件的问题，随时欢迎您咨询哦！如果有其他疑问，可以直接联系小易老师，我们随时为您服务！📚✨",
                        "您好！😊 如果有其他关于【E听说中学】软件使用的问题，随时联系我哦！也可以@小易老师，我们会尽快为您解答！📚✨",
                        "家长您好！😊 非常抱歉，这个问题我无法为您解答。如果有其他关于【E听说中学】软件使用的问题，随时可以联系我哦！您也可以@小易老师。期待为您提供帮助！📚✨"
                    ])
                    
                    # 更新提示语文本框
                    self.reply_text.delete('1.0', tk.END)
                    self.reply_text.insert('1.0', '\n'.join(self.replys))
                    
                    # 加载监控群列表
                    monitored_rooms = config.get('monitored_rooms', [])
                    for room in monitored_rooms:
                        new_id = f"monitored_{room['id']}"
                        self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                            text=room['name'],
                            values=(room['id'],))
                    
                    # 加载暂停群列表
                    self.paused_rooms = config.get('paused_rooms', [])
                    
                    # 加载管理员列表
                    admin_list = config.get('admin_list', [])
                    for admin in admin_list:
                        username = admin.get('username', "")
                        self.admin_friends_tree.insert('', 'end',
                            text=admin['realname'],
                            values=(admin['user_id'], admin['nickname'], username))
                    
                    # 加载通知列表
                    notify_list = config.get('notify_list', [])
                    for notify in notify_list:
                        self.notify_friends_tree.insert('', 'end',
                            text=notify['realname'],
                            values=(notify['user_id'], notify['nickname'], notify['username']))
                    
                    self.logger.info("配置加载成功")
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            # 初始化暂停群列表，确保默认存在
            self.paused_rooms = []

    def save_config(self):
        # 确保 replys 属性存在
        if not hasattr(self, 'replys'):
            self.replys = [
                "亲爱的家长，您好！😊 关于软件的问题，随时欢迎您咨询哦！如果有其他疑问，可以直接联系小易老师，我们随时为您服务！📚✨",
                "您好！😊 如果有其他关于【E听说中学】软件使用的问题，随时联系我哦！也可以@小易老师，我们会尽快为您解答！📚✨",
                "家长您好！😊 非常抱歉，这个问题我无法为您解答。如果有其他关于【E听说中学】软件使用的问题，随时可以联系我哦！您也可以@小易老师。期待为您提供帮助！📚✨"
            ]

        # 确保 paused_rooms 属性存在
        if not hasattr(self, 'paused_rooms'):
            self.paused_rooms = []

        config = {
            'api_key': self.api_key_entry.get().strip(),
            'bot_id': self.bot_id_entry.get().strip(),
            'require_at': self.require_at_var.get(),
            'replys': self.replys,
            'paused_rooms': self.paused_rooms,
            'monitored_rooms': [
                {
                    'name': self.monitored_rooms_tree.item(item)['text'],
                    'id': self.monitored_rooms_tree.item(item)['values'][0]
                }
                for item in self.monitored_rooms_tree.get_children()
            ],
            'admin_list': [
                {
                    'realname': self.admin_friends_tree.item(item)['text'],
                    'user_id': self.admin_friends_tree.item(item)['values'][0],
                    'nickname': self.admin_friends_tree.item(item)['values'][1],
                    'username': self.admin_friends_tree.item(item)['values'][2] if len(self.admin_friends_tree.item(item)['values']) > 2 else ""
                }
                for item in self.admin_friends_tree.get_children()
            ],
            'notify_list': [
                {
                    'realname': self.notify_friends_tree.item(item)['text'],
                    'user_id': self.notify_friends_tree.item(item)['values'][0],
                    'nickname': self.notify_friends_tree.item(item)['values'][1],
                    'username': self.notify_friends_tree.item(item)['values'][2] if len(self.notify_friends_tree.item(item)['values']) > 2 else ""
                }
                for item in self.notify_friends_tree.get_children()
            ]
        }
        try:
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            self.logger.info("配置保存成功")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")

    def add_selected_room(self):
        selected = self.all_rooms_tree.selection()
        for item in selected:
            name = self.all_rooms_tree.item(item, 'text')
            room_id = self.all_rooms_tree.item(item, 'values')[0]
            # 检查是否已经存在
            exists = False
            for existing in self.monitored_rooms_tree.get_children():
                if self.monitored_rooms_tree.item(existing)['values'][0] == room_id:
                    exists = True
                    break
            
            if not exists:
                # 使用新的ID以避免冲突
                new_id = f"monitored_{room_id}"
                self.monitored_rooms_tree.insert('', 'end', iid=new_id,
                    text=name,  # 显示群名称
                    values=(room_id,))  # 保存群ID
                self.logger.info(f"添加监控群：{name}")

    def remove_selected_room(self):
        selected = self.monitored_rooms_tree.selection()
        for item in selected:
            self.monitored_rooms_tree.delete(item)

    def refresh_room_list(self):
        if not self.startup or not self.startup.wechat:
            self.logger.error("微信未初始化，请重启程序")
            return
        
        try:
            self.logger.info("正在刷新群列表...")
            
            # 添加重试机制
            max_retries = 3
            rooms = None
            success = False
            
            # 方法1: 直接获取群列表
            for i in range(max_retries):
                try:
                    rooms = self.startup.wechat.get_rooms()
                    self.logger.info(f"{rooms}")
                    if rooms and 'room_list' in rooms and rooms['room_list']:
                        self.logger.info(f"方法1成功获取群列表，共 {len(rooms['room_list'])} 个群")
                        success = True
                        break
                    else:
                        self.logger.warning(f"方法1获取群列表为空，尝试重试... ({i + 1}/{max_retries})")
                        time.sleep(2)
                except Exception as e:
                    self.logger.warning(f"方法1获取群列表失败: {e}，尝试重试... ({i + 1}/{max_retries})")
                    time.sleep(2)
            
            # 方法2: 如果方法1失败，尝试使用搜索方式获取群
            if not success:
                self.logger.info("尝试方法2获取群列表...")
                try:
                    # 搜索所有群
                    search_result = self.startup.wechat.search_contacts("群")
                    if search_result and isinstance(search_result, list) and len(search_result) > 0:
                        # 构造与get_rooms相同格式的结果
                        room_list = []
                        for room in search_result:
                            if room.get('type') == 2:  # 群聊类型
                                room_list.append({
                                    'nickname': room.get('nickname', '未知群名'),
                                    'conversation_id': room.get('conversation_id', '')
                                })
                        
                        if room_list:
                            rooms = {'room_list': room_list}
                            self.logger.info(f"方法2成功获取群列表，共 {len(room_list)} 个群")
                            success = True
                except Exception as e:
                    self.logger.warning(f"方法2获取群列表失败: {e}")
            
            # 方法3: 如果前两种方法都失败，尝试获取会话列表
            if not success:
                self.logger.info("尝试方法3获取群列表...")
                try:
                    # 获取最近的会话列表
                    conversations = self.startup.wechat.get_conversations()
                    if conversations and isinstance(conversations, list) and len(conversations) > 0:
                        # 过滤出群聊会话
                        room_list = []
                        for conv in conversations:
                            if conv.get('type') == 2:  # 群聊类型
                                room_list.append({
                                    'nickname': conv.get('nickname', '未知群名'),
                                    'conversation_id': conv.get('conversation_id', '')
                                })
                        
                        if room_list:
                            rooms = {'room_list': room_list}
                            self.logger.info(f"方法3成功获取群列表，共 {len(room_list)} 个群")
                            success = True
                except Exception as e:
                    self.logger.warning(f"方法3获取群列表失败: {e}")

            if not success or not rooms or 'room_list' not in rooms or not rooms['room_list']:
                self.logger.error("所有方法都无法获取群列表，请检查企业微信状态")
                return

            # 清空现有列表
            for item in self.all_rooms_tree.get_children():
                self.all_rooms_tree.delete(item)
            
            # 添加新的群列表
            room_list = rooms.get('room_list', [])
            for room in room_list:
                try:
                    nickname = room.get('nickname', '未知群名')
                    conversation_id = room.get('conversation_id', '')
                    if not conversation_id:
                        continue
                    
                    self.all_rooms_tree.insert('', 'end', 
                        text=nickname,
                        values=(conversation_id,),
                        tags=('room',))
                except Exception as e:
                    self.logger.error(f"处理群信息时出错: {e}, 群数据: {room}")
                    continue
            
            self.logger.info(f"群列表刷新完成，共 {len(room_list)} 个群")
            
        except Exception as e:
            self.logger.error(f"刷新群列表失败: {str(e)}")

    def refresh_friend_list(self):
        if not self.startup:
            self.logger.error("微信未初始化，请重启程序")
            return
        
        try:
            self.logger.info("正在刷新好友列表...")
            # 清空现有列表
            for item in self.all_friends_tree.get_children():
                self.all_friends_tree.delete(item)
            
            # 添加重试机制
            max_retries = 3
            inner_contacts = None
            external_contacts = None
            
            # 获取内部联系人
            for i in range(max_retries):
                try:
                    inner_contacts = self.startup.wechat.get_inner_contacts()
                    if inner_contacts and 'user_list' in inner_contacts:
                        self.logger.info(f"成功获取内部联系人，共 {len(inner_contacts['user_list'])} 个")
                        break
                    else:
                        if i < max_retries - 1:
                            self.logger.warning(f"获取内部联系人为空或格式不正确，正在重试... ({i + 1}/{max_retries})")
                            time.sleep(2)
                except Exception as e:
                    if i < max_retries - 1:
                        self.logger.warning(f"获取内部联系人失败: {e}，正在重试... ({i + 1}/{max_retries})")
                        time.sleep(2)
                    else:
                        self.logger.error(f"获取内部联系人失败: {e}")
            
            # 获取外部联系人
            for i in range(max_retries):
                try:
                    external_contacts = self.startup.wechat.get_external_contacts()
                    if external_contacts and 'user_list' in external_contacts:
                        self.logger.info(f"成功获取外部联系人，共 {len(external_contacts['user_list'])} 个")
                        break
                    else:
                        if i < max_retries - 1:
                            self.logger.warning(f"获取外部联系人为空或格式不正确，正在重试... ({i + 1}/{max_retries})")
                            time.sleep(2)
                except Exception as e:
                    if i < max_retries - 1:
                        self.logger.warning(f"获取外部联系人失败: {e}，正在重试... ({i + 1}/{max_retries})")
                        time.sleep(2)
                    else:
                        self.logger.error(f"获取外部联系人失败: {e}")
            
            # 合并所有联系人
            all_contacts = []
            
            # 处理内部联系人
            if inner_contacts and 'user_list' in inner_contacts:
                all_contacts.extend(inner_contacts['user_list'])
            
            # 处理外部联系人
            if external_contacts and 'user_list' in external_contacts:
                all_contacts.extend(external_contacts['user_list'])
            
            if not all_contacts:
                self.logger.warning("未获取到任何联系人")
                return
            
            # 添加到树形视图
            contact_count = 0
            for contact in all_contacts:
                try:
                    # 确保contact是字典类型
                    if not isinstance(contact, dict):
                        self.logger.warning(f"跳过非字典类型的联系人数据: {type(contact)}")
                        continue
                    
                    realname = contact.get('realname', '')
                    nickname = contact.get('nickname', '')
                    user_id = contact.get('user_id', '')
                    username = contact.get('username', '')
                    
                    if user_id:  # 只添加有效的联系人
                        self.all_friends_tree.insert('', 'end',
                            text=realname,
                            values=(user_id, nickname, username))
                        contact_count += 1
                except Exception as e:
                    self.logger.error(f"处理联系人信息时出错: {e}, 数据: {contact}")
                    continue
            
            self.logger.info(f"好友列表刷新完成，成功加载 {contact_count} 个联系人")
            
        except Exception as e:
            self.logger.error(f"刷新好友列表失败: {str(e)}")
            self.logger.error("错误详情: ", exc_info=True)

    def add_selected_friend(self):
        selected = self.all_friends_tree.selection()
        for item in selected:
            realname = self.all_friends_tree.item(item, 'text')
            values = self.all_friends_tree.item(item, 'values')
            user_id = values[0]
            nickname = values[1]
            username = values[2] if len(values) > 2 else ""
            
            # 检查是否已经存在
            exists = False
            for existing in self.admin_friends_tree.get_children():
                if self.admin_friends_tree.item(existing)['values'][0] == user_id:
                    exists = True
                    break
            
            if not exists:
                self.admin_friends_tree.insert('', 'end',
                    text=realname,
                    values=(user_id, nickname, username))
                self.logger.info(f"添加管理员：{realname} ({username})")

    def remove_selected_friend(self):
        selected = self.admin_friends_tree.selection()
        for item in selected:
            self.admin_friends_tree.delete(item)

    def add_notify_friend(self):
        selected = self.all_friends_tree.selection()
        for item in selected:
            realname = self.all_friends_tree.item(item, 'text')
            values = self.all_friends_tree.item(item, 'values')
            user_id = values[0]
            nickname = values[1]
            username = values[2] if len(values) > 2 else ""
            
            # 检查是否已经存在
            exists = False
            for existing in self.notify_friends_tree.get_children():
                if self.notify_friends_tree.item(existing)['values'][0] == user_id:
                    exists = True
                    break
            
            if not exists:
                self.notify_friends_tree.insert('', 'end',
                    text=realname,
                    values=(user_id, nickname, username))
                self.logger.info(f"添加通知好友：{realname} ({username})")

    def remove_notify_friend(self):
        selected = self.notify_friends_tree.selection()
        for item in selected:
            self.notify_friends_tree.delete(item)

    # 添加重置按钮状态的方法
    def _reset_button_state(self):
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")

    def setup_scheduler_tab(self):
        """设置定时任务标签页"""
        self.scheduler_frame = SchedulerTab(self.scheduler_tab, self.scheduler, self)
        self.scheduler_frame.pack(fill=tk.BOTH, expand=True)


# 修改StartUp类
class StartUp:
    def __init__(self, gui_instance):
        self.logger = logging.getLogger()
        self.wechat = ntwork.WeWork()
        self.exit_flag = False
        self.user_id = None
        self.name = None
        self.gui = gui_instance
        self.require_at = gui_instance.require_at_var.get()
        # 获取GUI实例中的提示语列表
        self.replys = gui_instance.replys
        # 获取GUI实例中的暂停群列表
        self.paused_rooms = gui_instance.paused_rooms

    def on_recv_message(self, wechat_instance: ntwork.WeWork, message):
        # 添加消息接收日志
        self.logger.info(f"收到原始消息: {json.dumps(message, ensure_ascii=False)}")
        self.logger.info(f"机器人名字: {self.name}")
        if not hasattr(self, 'api_key') or not hasattr(self, 'bot_id'):
            self.logger.error("Coze API参数未初始化")
            return
            
        data = message["data"]
        content = data.get("content", "").strip()
        if not content:
            return
        room_wxid = data.get("conversation_id")
        from_wxid = data.get("sender")
        sender_name = data.get("sender_name", "")
        # 获取所有通知好友的会话ID
        notify_users = [
            f"S:{self.user_id}_{self.gui.notify_friends_tree.item(item)['values'][0]}"
            for item in self.gui.notify_friends_tree.get_children()
        ]
        self.logger.info(f"待通知用户列表: {notify_users}")
        if room_wxid=="FILEASSIST":
            return
        # 添加消息类型检查日志
        if data.get("content_type") not in [0,2]:
            self.logger.info(f"忽略非文本消息，类型: {data.get('content_type')}")
            return
        # 添加消息基础信息日志
        self.logger.info(f"消息处理开始 | 发送者: {sender_name}({from_wxid}) | 会话ID: {room_wxid} | 内容: {content}")
        
        # 根据room_wxid前缀判断是群聊还是私聊
        is_private = room_wxid and room_wxid.startswith('S:')
        is_group = room_wxid and room_wxid.startswith('R:')
        
        self.logger.info(f"消息类型: {'私聊' if is_private else '群聊' if is_group else '未知类型'}")
        
        # 如果是群聊，获取机器人在群中的昵称
        if is_group:
            try:
                room_members = wechat_instance.get_room_members(room_wxid)
                for member in room_members['member_list']:
                    if member['username'] == self.name:
                        if member['room_nickname'] == '':
                            self.nickname = self.name
                        else:
                            self.nickname = member['room_nickname']
                        break
            except Exception as e:
                self.logger.warning(f"获取群成员信息失败: {e}")
                self.nickname = self.name
        else:
            # 私聊使用默认昵称
            self.nickname = self.name

        # 获取管理员列表
        admin_list = [
            str(self.gui.admin_friends_tree.item(item)['values'][0]).strip()
            for item in self.gui.admin_friends_tree.get_children()
            if len(self.gui.admin_friends_tree.item(item)['values']) > 0
        ]
        self.logger.info(f"当前管理员列表: {admin_list}")
        
        # 获取监控群列表
        monitored_rooms = [
            self.gui.monitored_rooms_tree.item(item)['values'][0]
            for item in self.gui.monitored_rooms_tree.get_children()
        ]
        self.logger.info(f"当前监控群列表: {monitored_rooms}")
        
        # 处理管理员私聊的恢复命令
        if is_private and from_wxid in admin_list:
            # 恢复指定群的自动回复功能
            if content.startswith("恢复群回复"):
                self.logger.info(f"检测到管理员恢复群回复命令: {content}")
                # 提取群ID
                parts = content.split("恢复群回复", 1)
                if len(parts) > 1:
                    target_room_id = parts[1].strip()
                    self.logger.info(f"管理员 {from_wxid} 请求恢复群 {target_room_id} 的自动回复")
                    
                    # 检查该群是否在暂停列表中
                    if target_room_id in self.gui.paused_rooms:
                        self.gui.paused_rooms.remove(target_room_id)
                        
                        # 在主线程中保存配置
                        # def update_ui():
                        #     self.gui.save_config()
                        
                        # self.gui.root.after(0, update_ui)
                        
                        # 获取群名称
                        room_name = "未知群名"
                        for item in self.gui.all_rooms_tree.get_children():
                            if self.gui.all_rooms_tree.item(item)['values'][0] == target_room_id:
                                room_name = self.gui.all_rooms_tree.item(item)['text']
                                break
                        
                        wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', f"已恢复群 {room_name} 的自动回复\n群ID: {target_room_id}")
                        self.logger.info(f"已恢复群 {room_name} ({target_room_id}) 的自动回复")
                    else:
                        wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', f"群 {target_room_id} 未处于暂停状态")
                else:
                    wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', "请指定要恢复的群ID，格式: 恢复群回复R:xxxx")
                return
            elif content.startswith("恢复"):
                self.logger.info(f"检测到管理员恢复命令: {content}")
                # 提取群ID
                parts = content.split("恢复", 1)
                if len(parts) > 1:
                    target_room_id = parts[1].strip()
                    self.logger.info(f"管理员 {from_wxid} 请求恢复群 {target_room_id} 的监控")
                    
                    # 检查该群是否已在监控列表中
                    if target_room_id in monitored_rooms:
                        wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', f"群 {target_room_id} 已在监控列表中")
                        return
                    
                    # 尝试获取群信息
                    try:
                        # 获取群名称
                        room_info = None
                        try:
                            # 尝试获取群信息
                            room_info = wechat_instance.get_room_info(target_room_id)
                        except:
                            pass
                        
                        room_name = "未知群名"
                        if room_info and 'nickname' in room_info:
                            room_name = room_info['nickname']
                        
                        # 添加到监控列表
                        new_id = f"monitored_{target_room_id}"
                        
                        # 在主线程中更新UI
                        def update_ui():
                            self.gui.monitored_rooms_tree.insert('', 'end', iid=new_id,
                                text=room_name,
                                values=(target_room_id,))
                            # 保存配置
                            self.gui.save_config()
                            
                        self.gui.root.after(0, update_ui)
                        
                        wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', f"已将群 {room_name} ({target_room_id}) 添加到监控列表")
                        self.logger.info(f"管理员 {from_wxid} 已恢复群 {room_name} ({target_room_id}) 的监控")
                    except Exception as e:
                        self.logger.error(f"恢复群监控失败: {e}")
                        wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', f"恢复群监控失败: {str(e)}")
                return
        
        # 群聊消息处理
        if is_group:
            self.logger.info(f"群消息检查 | 群ID: {room_wxid} | 是否在监控列表: {room_wxid in monitored_rooms}")
            self.logger.info(f"是否在暂停列表: {room_wxid in self.gui.paused_rooms}")

            # 检查群消息是否在监控列表中
            if room_wxid not in monitored_rooms:
                self.logger.info(f"群 {room_wxid} 不在监控列表中，忽略消息")
                return
                
            # 管理员在监控群中发言，自动暂停该群的自动回复
            if from_wxid in admin_list and room_wxid in monitored_rooms and room_wxid not in self.gui.paused_rooms:
                self.logger.info(f"检测到管理员在监控群中发言，暂停该群自动回复 | 群ID: {room_wxid}")
                
                # 添加到暂停列表
                self.gui.paused_rooms.append(room_wxid)
                
                # 在主线程中保存配置
                # def update_ui():
                #     self.gui.save_config()
                
                # self.gui.root.after(0, update_ui)
                
                # 获取群名称
                room_name = "未知群名"
                for item in self.gui.all_rooms_tree.get_children():
                    if self.gui.all_rooms_tree.item(item)['values'][0] == room_wxid:
                        room_name = self.gui.all_rooms_tree.item(item)['text']
                        break
                
                # 私聊通知管理员
                wechat_instance.send_text(f'S:{self.user_id}_{from_wxid}', 
                    f"检测到您在群 {room_name} 中发言，已自动暂停该群的自动回复\n"
                    f"群ID: {room_wxid}\n"
                    f"恢复命令: 恢复群回复{room_wxid}")
                
                self.logger.info(f"已暂停群 {room_name} ({room_wxid}) 的自动回复")
                return
                
            # 检查群是否在暂停列表中
            if room_wxid in self.gui.paused_rooms:
                self.logger.info(f"群 {room_wxid} 在暂停回复列表中，忽略消息")
                return
        
        # 检查是否是自己发送的消息
        if from_wxid == self.user_id:
            self.logger.info("忽略自己发送的消息")
            return
        
        # 检查是否需要@机器人（仅群聊需要）
        is_at_me = False
        if is_group and "@" in content:
            self.logger.info("群聊消息包含@符号，进行检查...")
            if f"@{self.nickname}" in content:
                is_at_me = True
                content = content.replace(f"@{self.nickname}", "").strip()
                self.logger.info(f"检测到@机器人的消息，处理后内容: {content}")
                
        # 群聊中非管理员需要@机器人才回复
        if is_group and self.require_at and not is_at_me:
            if from_wxid in admin_list:
                self.logger.info("群聊中管理员消息无需@，继续处理")
            else:
                self.logger.info("群聊中非管理员消息且未@机器人，已忽略")
                return
                
        try:
            # 生成会话ID，可以使用群ID+发送者ID的组合，私聊直接使用发送者ID
            conversation_id = f"{room_wxid}_{from_wxid}" if is_group else from_wxid
            
            # API调用日志
            self.logger.info(f"调用Coze API | 内容长度: {len(content)} | 会话ID: {conversation_id}")
            import coze_api
            
            response = coze_api.coze_api(
                self.api_key, 
                self.bot_id, 
                content,
                from_wxid,
                conversation_id
            )
            self.logger.info(f"API响应原始数据: {response}")
            
            if response:
                response="\n".join([line for line in response.split("\\n") if line.strip()])
                # 添加回复日志
                self.logger.info(f"准备发送回复 | 内容长度: {len(response)}")
                
                # 确定发送目标：群聊发送到群，私聊发送到用户
                # 注意：保持原有的发送格式，私聊使用 'S:user_id_from_wxid'
                target_id = room_wxid if is_group else room_wxid  # 私聊时room_wxid已经包含了S:前缀
                
                # 使用实例中的提示语列表
                if response in self.replys and is_private:
                    # 给每个通知好友发送消息
                    for target_id in notify_users:
                        try:
                            if not target_id.startswith("S:"):
                                self.logger.warning(f"无效的会话ID格式: {target_id}")
                                continue
                            wechat_instance.send_text(
                                conversation_id=target_id,
                                content=f"===机器人无法回复，请注意查看===\n发送者:\n {sender_name}({from_wxid}) \n 会话ID: {room_wxid} \n 内容: {content}"
                            )
                        except Exception as e:
                            self.logger.error(f"发送通知到 {target_id} 失败: {str(e)}")
                    return
                wechat_instance.send_text(target_id,response )
                self.logger.info(f"消息回复成功，发送至: {target_id}")
            else:
                self.logger.warning("API返回空响应，未发送回复")
                
        except Exception as e:
            self.logger.error(f"消息处理异常: {str(e)}", exc_info=True)

    def exit_program(self):
        self.logger.info("正在退出程序...")
        self.exit_flag = True
        keyboard.unhook_all()
        ntwork.exit_()


def main():
    try:
        app = WeChatGUI()
        app.run()
    except Exception as e:
        import traceback
        print(f"程序启动失败: {e}")
        print(traceback.format_exc())
        input("按回车键退出...")


if __name__ == "__main__":
    main() 