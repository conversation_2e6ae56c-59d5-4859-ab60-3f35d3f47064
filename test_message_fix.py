# -*- coding: utf-8 -*-
"""
消息处理修复测试
用于验证消息处理方法的参数传递是否正确
"""

import json
import time
import logging

# 模拟ntwork库的消息结构
def create_test_message():
    """创建测试消息"""
    return {
        "data": {
            "local_id": "test_123",
            "server_id": "server_456", 
            "content": "测试消息",
            "conversation_id": "R:test_room",
            "sender": "test_user",
            "sender_name": "测试用户",
            "content_type": 0
        }
    }

class MockWeWork:
    """模拟WeWork对象"""
    def __init__(self):
        self.name = "MockWeWork"
        
    def get_login_info(self):
        return {"user_id": "test_user", "nickname": "测试机器人"}

def test_message_processing():
    """测试消息处理"""
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 创建测试消息和微信实例
    test_message = create_test_message()
    mock_wechat = MockWeWork()
    
    logger.info("开始测试消息处理...")
    logger.info(f"测试消息: {json.dumps(test_message, ensure_ascii=False)}")
    
    # 测试消息ID提取
    try:
        message_id = test_message["data"].get("local_id") or test_message["data"].get("server_id")
        logger.info(f"成功提取消息ID: {message_id}")
    except Exception as e:
        logger.error(f"提取消息ID失败: {e}")
        return False
    
    # 测试消息内容提取
    try:
        data = test_message["data"]
        content = data.get("content", "").strip()
        room_wxid = data.get("conversation_id")
        from_wxid = data.get("sender")
        sender_name = data.get("sender_name", "")
        
        logger.info(f"消息内容: {content}")
        logger.info(f"群ID: {room_wxid}")
        logger.info(f"发送者: {sender_name}({from_wxid})")
        
    except Exception as e:
        logger.error(f"提取消息内容失败: {e}")
        return False
    
    logger.info("消息处理测试通过!")
    return True

def test_callback_signature():
    """测试回调函数签名"""
    
    def correct_callback(wechat_instance, message):
        """正确的回调函数签名"""
        print(f"WeChat实例: {type(wechat_instance)}")
        print(f"消息类型: {type(message)}")
        print(f"消息内容: {message.get('data', {}).get('content', 'N/A')}")
        return True
    
    def incorrect_callback(message, wechat_instance):
        """错误的回调函数签名"""
        print(f"这会导致参数错误")
        return False
    
    # 模拟ntwork的回调调用方式
    mock_wechat = MockWeWork()
    test_message = create_test_message()
    
    print("测试正确的回调签名:")
    try:
        result = correct_callback(mock_wechat, test_message)
        print(f"正确签名测试结果: {result}")
    except Exception as e:
        print(f"正确签名测试失败: {e}")
    
    print("\n测试错误的回调签名:")
    try:
        # 这会导致错误，因为参数顺序不对
        result = incorrect_callback(mock_wechat, test_message)  # 故意传错参数
        print(f"错误签名测试结果: {result}")
    except Exception as e:
        print(f"错误签名测试失败（预期）: {e}")

if __name__ == "__main__":
    print("=== 消息处理修复测试 ===")
    
    print("\n1. 测试消息处理逻辑:")
    test_message_processing()
    
    print("\n2. 测试回调函数签名:")
    test_callback_signature()
    
    print("\n=== 测试完成 ===")
    print("\n修复建议:")
    print("1. 确保回调函数签名为: def callback(wechat_instance, message)")
    print("2. 消息数据通过 message['data'] 访问")
    print("3. 避免复杂的异步处理，直接在回调中处理消息")
    print("4. 添加适当的异常处理和日志记录")
