#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试上传监控群和监控好友功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os

def test_file_dialog():
    """测试文件对话框功能"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 测试选择群文件
    print("测试选择群文件...")
    file_path = filedialog.askopenfilename(
        title="选择监控群列表文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
    )
    
    if file_path:
        print(f"选择的文件: {file_path}")
        
        # 读取文件内容
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
            
            print(f"文件内容 ({len(lines)} 行):")
            for i, line in enumerate(lines, 1):
                print(f"  {i}. {line}")
                
        except Exception as e:
            print(f"读取文件失败: {e}")
    else:
        print("未选择文件")
    
    root.destroy()

def test_name_matching():
    """测试名称匹配逻辑"""
    print("\n测试名称匹配逻辑...")
    
    # 模拟群列表
    mock_groups = [
        {"nickname": "测试群1", "conversation_id": "R:123456"},
        {"nickname": "测试群2", "conversation_id": "R:123457"},
        {"nickname": "客服群", "conversation_id": "R:123458"},
        {"nickname": "技术支持群", "conversation_id": "R:123459"},
    ]
    
    # 模拟好友列表
    mock_friends = [
        "邾俊勇 (S:1688854710715177)",
        "林铭清[LinMingQing](S:1688854803641027)",
        "陆冰清 (S:1688855669419370)",
        "刘羿[DuoDuoTuanZhang](S:1688854489470333)",
    ]
    
    # 测试群名称匹配
    search_groups = ["测试群1", "客服群", "不存在的群"]
    print("群名称匹配测试:")
    for group_name in search_groups:
        found = False
        for group in mock_groups:
            if group.get('nickname', '') == group_name:
                print(f"  ✓ 找到群: {group_name} -> {group['conversation_id']}")
                found = True
                break
        if not found:
            print(f"  ✗ 未找到群: {group_name}")
    
    # 测试好友名称匹配
    search_friends = ["邾俊勇", "LinMingQing", "不存在的好友"]
    print("\n好友名称匹配测试:")
    for friend_name in search_friends:
        found = False
        for friend_text in mock_friends:
            # 检查真实姓名匹配
            if friend_text.startswith(friend_name):
                if ("[" in friend_text and friend_text.split("[")[0] == friend_name) or \
                   ("(" in friend_text and friend_text.split("(")[0] == friend_name):
                    print(f"  ✓ 找到好友(真实姓名): {friend_name} -> {friend_text}")
                    found = True
                    break
            # 检查用户名匹配
            elif "[" in friend_text and "]" in friend_text:
                username_part = friend_text.split("[")[1].split("]")[0]
                if username_part == friend_name:
                    print(f"  ✓ 找到好友(用户名): {friend_name} -> {friend_text}")
                    found = True
                    break
        if not found:
            print(f"  ✗ 未找到好友: {friend_name}")

def main():
    """主测试函数"""
    print("=== 上传监控群和监控好友功能测试 ===")
    
    # 检查示例文件是否存在
    group_file = "监控群列表示例.txt"
    friend_file = "监控好友列表示例.txt"
    
    print(f"\n检查示例文件:")
    print(f"  群列表文件: {group_file} - {'存在' if os.path.exists(group_file) else '不存在'}")
    print(f"  好友列表文件: {friend_file} - {'存在' if os.path.exists(friend_file) else '不存在'}")
    
    # 测试名称匹配逻辑
    test_name_matching()
    
    # 测试文件对话框（需要用户交互）
    choice = input("\n是否测试文件对话框功能？(y/n): ")
    if choice.lower() == 'y':
        test_file_dialog()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
