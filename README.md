# 微信机器人 FastAPI 接口

这是一个基于FastAPI框架的微信机器人HTTP接口服务，用于替代原有的CURL请求方式，提供更灵活、更稳定的API调用方式。

## 功能特点

- 提供RESTful API接口，支持JSON和表单两种请求方式
- 支持XML数据处理，自动提取内容
- 完整的错误处理和日志记录
- 支持CORS跨域请求
- 内置健康检查接口
- 提供API文档（基于Swagger UI）
- 服务管理工具，支持启动、停止、重启和状态查询
- 兼容Pydantic V2，修复了配置警告
- 统一响应格式，与原服务保持一致

## 安装依赖

```bash
pip install fastapi uvicorn httpx psutil
```

## 快速开始

1. 克隆或下载本项目代码

2. 配置服务地址

   在 `weixinrobot_api.py` 文件中修改以下配置项：

   ```python
   # 服务配置
   ROBOT_URL = "http://***************/weixinrobot/index"  # 修改为实际的微信机器人服务地址
   HTTP_PORT = 80  # 修改为你希望API服务监听的端口
   ```

3. 启动API服务

   **方法1**: 直接运行
   ```bash
   python weixinrobot_api.py
   ```

   **方法2**: 使用uvicorn启动
   ```bash
   uvicorn weixinrobot_api:app --host 0.0.0.0 --port 80
   ```

   **方法3**: 使用服务管理脚本
   ```bash
   # 启动服务
   python run_service.py start
   
   # 停止服务
   python run_service.py stop
   
   # 重启服务
   python run_service.py restart
   
   # 查看服务状态
   python run_service.py status
   ```

4. 访问API文档

   启动服务后，访问 [http://localhost:80/docs](http://localhost:80/docs) 查看API文档

## 服务管理

项目提供了 `run_service.py` 脚本用于管理API服务，支持以下功能：

- **启动服务**: 将API服务作为后台进程启动，并将输出重定向到日志文件
- **停止服务**: 停止正在运行的API服务
- **重启服务**: 停止并重新启动API服务
- **状态查询**: 显示服务运行状态、运行时间和资源使用情况

### 服务管理脚本特点

1. 自动跟踪服务进程ID
2. 进程状态监控
3. 资源使用统计（CPU和内存）
4. 详细的日志记录
5. 优雅的进程终止处理

### 日志文件

服务管理脚本会生成以下日志文件：

- `weixinrobot_service.log`: 服务管理脚本的日志
- `weixinrobot_api_output.log`: API服务的输出日志

## API接口说明

所有API接口都返回统一的响应格式: `{"msg": "响应消息"}`

### 1. 发送普通消息

- **接口地址**: `/weixinrobot/send`
- **请求方式**: POST
- **请求格式**: JSON
- **请求参数**:
  - `content`: 消息内容
  - `nickname`: 用户昵称
  - `UserName`: 用户ID (注意这里区分大小写)

示例请求:

```json
{
  "content": "你好小王，你见过吗",
  "nickname": "string",
  "UserName": "string"
}
```

示例响应:

```json
{
  "msg": "微信回复"
}
```

### 2. 表单方式发送消息

- **接口地址**: `/weixinrobot/send-form`
- **请求方式**: POST
- **请求格式**: Form表单
- **请求参数**:
  - `content`: 消息内容
  - `nickname`: 用户昵称
  - `username`: 用户ID

### 3. 发送XML消息

- **接口地址**: `/weixinrobot/send-xml`
- **请求方式**: POST
- **请求格式**: JSON
- **请求参数**:
  - `xmldata`: XML数据，格式为字典
  - `nickname`: 用户昵称
  - `UserName`: 用户ID (注意这里区分大小写)

示例请求:

```json
{
  "xmldata": {
    "data": [
      {
        "StrContent": "测试XML内容"
      }
    ]
  },
  "nickname": "测试用户",
  "UserName": "user123"
}
```

### 4. 发送原始请求

- **接口地址**: `/weixinrobot/raw`
- **请求方式**: POST
- **请求格式**: JSON
- **请求参数**: 任意格式的JSON对象

### 5. 健康检查

- **接口地址**: `/health`
- **请求方式**: GET
- **返回格式**: JSON

## API测试

您可以使用各种HTTP客户端工具测试API，如Postman、Apifox或浏览器插件。

示例测试请求与响应：

### 请求
```json
{
  "content": "你好小王，你见过吗",
  "nickname": "string",
  "UserName": "string"
}
```

### 响应
```json
{
  "msg": "微信回复"
}
```

## 客户端示例

项目包含了客户端示例代码 `weixinrobot_client.py`，可以用来测试API的功能。

运行客户端示例：

```bash
# 普通模式
python weixinrobot_client.py

# 调试模式（显示更详细的日志）
python weixinrobot_client.py --debug
```

示例代码演示了三种不同的请求方式：

1. 发送普通JSON消息
2. 发送XML格式消息
3. 使用表单发送消息

### 客户端特点

1. 完善的错误处理
2. 详细的日志记录
3. 支持调试模式
4. 友好的输出格式
5. 自动检测API健康状态

## 与CURL代码对比

原始CURL代码：

```php
$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_PORT => "80",
    CURLOPT_URL => "http://***************/weixinrobot/index",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => http_build_query(array('content'=>trim($xmldata['data'][0]['StrContent']),'nickname'=>$nickname,'UserName'=>$UserName)),
    CURLOPT_HTTPHEADER => [
        "content-type: application/x-www-form-urlencoded"
    ],
]);
```

转换为FastAPI后的代码：

```python
@app.post("/weixinrobot/send-xml")
async def send_xml_message(request: XmlMessageRequest):
    # 提取XML数据中的StrContent
    content = ""
    try:
        if "data" in request.xmldata and len(request.xmldata["data"]) > 0:
            content = request.xmldata["data"][0].get("StrContent", "")
    except (KeyError, IndexError) as e:
        logger.warning(f"无法提取XML内容: {e}")
    
    # 构建请求数据
    data = {
        'content': content,
        'nickname': request.nickname,
        'UserName': request.UserName
    }
    
    result = await send_to_weixinrobot(data)
    return result
```

## 常见问题与解决方案

### 1. 422 Unprocessable Entity 错误

如果您收到 422 错误，这通常意味着请求参数验证失败。确保：

- 请求参数名称大小写正确，特别是 `UserName` 参数（注意大写 N）
- 所有必需的字段都已提供
- 字段的数据类型正确

### 2. API连接失败

如果无法连接到API服务：

- 确保服务已经启动
- 检查端口是否被其他程序占用
- 检查防火墙是否允许访问该端口
- 使用 `python run_service.py status` 查看服务状态

### 3. 返回非JSON响应

有时API可能返回非JSON格式的响应：

- 检查原始微信机器人服务是否正常运行
- 查看API日志中的详细错误信息
- 客户端使用 `--debug` 参数运行，获取更详细的响应内容

## 生产环境部署

对于生产环境部署，建议使用以下方式：

1. **使用 Supervisor**: 使用 Supervisor 来管理服务进程，确保在服务崩溃时自动重启。

   ```bash
   # 安装 Supervisor
   pip install supervisor
   
   # 创建 Supervisor 配置文件
   echo "[program:weixinrobot]
   command=python /path/to/weixinrobot_api.py
   directory=/path/to/project
   autostart=true
   autorestart=true
   stderr_logfile=/path/to/weixinrobot.err.log
   stdout_logfile=/path/to/weixinrobot.out.log" > /etc/supervisor/conf.d/weixinrobot.conf
   
   # 重新加载配置并启动服务
   supervisorctl reread
   supervisorctl update
   supervisorctl start weixinrobot
   ```

2. **使用反向代理**: 在生产环境中，建议使用 Nginx 或 Apache 作为反向代理，提供SSL加密和负载均衡。

   Nginx 配置示例:
   ```
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## 故障排除

如果遇到问题，请检查：

1. 确保微信机器人服务地址（ROBOT_URL）正确
2. 检查日志输出，了解错误详情
3. 使用健康检查接口测试API服务是否正常运行
4. 如果使用服务管理脚本，可以通过 `python run_service.py status` 查看服务状态
5. 使用调试模式运行客户端 `python weixinrobot_client.py --debug` 获取更详细的诊断信息

## 许可证

MIT 