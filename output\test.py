import requests

def poll_messages(last_id=0):
    response = requests.get(f"http://localhost:8002/api/messages/poll?last_id={last_id}")
    if response.status_code == 200:
        data = response.json()
        return data["data"]["last_id"], data["data"]["message"]
    return None, None

# 使用示例
last_id = 0
while True:
    last_id, message = poll_messages(last_id)
    if message:
        print(f"收到新消息: {message}")